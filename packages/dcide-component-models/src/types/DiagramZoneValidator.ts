import { DiagramValidationError, DiagramValidator } from './DiagramValidator';

import { DiagramComponentInstance } from './DiagramComponentInstance';
import { Zone } from './Zone';
import { Diagram } from './Diagram';

export enum ZoneValidationErrorType {
    CONTROL_METHODS_NO_CONTROL_COMPONENT = 'CONTROL_METHODS_NO_CONTROL_COMPONENT',
    CONTROL_METHODS_NO_CONTROL_COMPONENT_M = 'CONTROL_METHODS_NO_CONTROL_COMPONENT_M',
    CONTROL_METHODS_NO_CONTROL_COMPONENT_Lp = 'CONTROL_METHODS_NO_CONTROL_COMPONENT_Lp',
    CONTROL_METHODS_NO_CONTROL_COMPONENT_Ln = 'CONTROL_METHODS_NO_CONTROL_COMPONENT_Ln',
    CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS = 'CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS',
    CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_M = 'CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_M',
    CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_M_Ln = 'CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_M_Ln',
    CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_Ln = 'CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_Ln',
    CONTROL_METHODS_NO_CV_AND_DV_COMBINED = 'CONTROL_METHODS_NO_CV_AND_DV_COMBINED',
    CONTROL_METHODS_NO_CV_AND_DV_COMBINED_Lp_M = 'CONTROL_METHODS_NO_CV_AND_DV_COMBINED_Lp_M',
    CONTROL_METHODS_NO_CV_AND_DV_COMBINED_M_Ln = 'CONTROL_METHODS_NO_CV_AND_DV_COMBINED_M_Ln',
    CONTROL_METHODS_NO_CV_AND_DV_COMBINED_Lp_Ln = 'CONTROL_METHODS_NO_CV_AND_DV_COMBINED_Lp_Ln',
    VOLTAGE_LEVELS_MISMATCH = 'VOLTAGE_LEVELS_MISMATCH',
    VOLTAGE_TYPES_MISMATCH = 'VOLTAGE_TYPES_MISMATCH',
    VOLTAGE_TYPES_MISSING = 'VOLTAGE_TYPES_MISSING',
    GROUNDING_MISSING = 'GROUNDING_MISSING',
    GROUNDING_CONFIGURATION_MISSING = 'GROUNDING_CONFIGURATION_MISSING',
    GROUNDING_MULTIPLE = 'GROUNDING_CONFIGURATION_MULTIPLE',
}

export type ZoneValidationError = DiagramValidationError<
    ZoneValidationErrorType,
    {
        zone: Zone;
        componentInstances: DiagramComponentInstance[];
        voltage?: { min: number; max: number };
    }
>;

export type ZoneValidator = DiagramValidator<
    'zone',
    { diagram: Diagram; zones: Zone[] },
    ZoneValidationError,
    ZoneValidationErrorType
>;
