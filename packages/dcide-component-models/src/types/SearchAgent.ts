import { z } from 'zod';

import { RTESchema } from '../validators';

export const SearchAgentMessageSchema = z.object({
    id: z.string(),
    role: z.enum(['system', 'user', 'assistant', 'developer']),
    content: z.string(),
});

export const SearchAgentUserMessage = SearchAgentMessageSchema.extend({
    role: z.literal('user'),
});

export const SearchAgentAssistantMessage = SearchAgentMessageSchema.extend({
    role: z.literal('assistant'),
});

export const SearchAgentBaseMessageSchema = z.object({
    id: z.string(),
    message: z.string(),
    time: z.number(),
});

export const SearchAgentErrorSchema = SearchAgentBaseMessageSchema.extend({
    type: z.literal('error'),
});

export const SearchAgentQuestionSchema = SearchAgentBaseMessageSchema.extend({
    type: z.literal('user'),
});

export const SearchAgentAnswerSchema = SearchAgentBaseMessageSchema.extend({
    type: z.literal('assistant'),
    questionId: z.string().optional(),
});

export const IncomingAIMessageSchema = z.object({
    id: z.string(),
    message: z.string(),
});

export const SearchAgentResponseSchema = z.object({
    content: z.array(
        z.object({
            type: z.enum(['command', 'text']),
            body: z.string(),
        }),
    ),
});

export type SearchAgentMessage = z.infer<typeof SearchAgentMessageSchema>;
export type SearchAgentUserMessage = z.infer<typeof SearchAgentUserMessage>;
export type SearchAgentAssistantMessage = z.infer<typeof SearchAgentAssistantMessage>;

export type SearchAgentBaseMessage = z.infer<typeof SearchAgentBaseMessageSchema>;
export type SearchAgentError = z.infer<typeof SearchAgentErrorSchema>;
export type SearchAgentQuestion = z.infer<typeof SearchAgentQuestionSchema>;
export type SearchAgentAnswer = z.infer<typeof SearchAgentAnswerSchema>;
export type IncomingAIMessage = z.infer<typeof IncomingAIMessageSchema>;

export type SearchAgentResponse = z.infer<typeof SearchAgentResponseSchema>;

export type SearchThread = {
    id: string;
    thread: {
        input: {
            id?: string;
            role: 'system' | 'user' | 'assistant' | 'developer';
            content: string;
        }[];
    };
    createdAt: string;
    createdBy: string;
    email?: string | null;
    exhibitorMatch?: string | null;
    internal: boolean;
    event?: string | null;
};

export type ExhibitorMatch = {
    id: string;
    thread?: SearchThread | null;
    summarizeMatchResult?: string | null;
    requirements?: string | null;
    productSearchResults?: string[] | null;
    companySearchResults?: string[] | null;
    caseStudiesResults?: string[] | null;
    internetProductResults?: string | null;
    sentToCompanies?: {
        company: string;
        lead: string;
    }[];
    event?: string | null;
    createdAt: string;
};

export type ExhibitorMatchLead = {
    id: string;
    exhibitorMatch: string;
    company: string;
    requirements: z.infer<typeof RTESchema>;
    email?: string | null;
    user?: string | null;
    event?: string | null;
    createdAt: string;
};
