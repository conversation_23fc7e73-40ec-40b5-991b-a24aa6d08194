import {
    BatteryPayload,
    BusPayload,
    CapacitorPayload,
    ChargerPayload,
    CircuitBreakerPayload,
    ContactorPayload,
    ConverterPayload,
    CustomPayload,
    DisconnectPayload,
    FusePayload,
    GeneratorPayload,
    GroundingPayload,
    HVACPayload,
    HydroPayload,
    LoadPayload,
    LightPayload,
    MeterPayload,
    MotorPayload,
    PanelPayload,
    SolarPayload,
    TransformerPayload,
    UtilityPayload,
    WindPayload,
} from '../validators/components';

export type ComponentPayload =
    | BatteryPayload
    | BusPayload
    | CapacitorPayload
    | ChargerPayload
    | CircuitBreakerPayload
    | ContactorPayload
    | ConverterPayload
    | CustomPayload
    | DisconnectPayload
    | FusePayload
    | GeneratorPayload
    | GroundingPayload
    | HVACPayload
    | HydroPayload
    | LoadPayload
    | LightPayload
    | MeterPayload
    | MotorPayload
    | PanelPayload
    | SolarPayload
    | TransformerPayload
    | UtilityPayload
    | WindPayload;
