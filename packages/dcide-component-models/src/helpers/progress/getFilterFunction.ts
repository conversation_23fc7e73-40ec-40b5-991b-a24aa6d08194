import { get } from 'radash';
import { ComponentSection } from '../../types';

export type FilterFunction = (name: string, value: unknown, values: object) => boolean;

const getFilterFunction = (section: ComponentSection) => {
    switch (section) {
        case 'electrical':
            return filterElectricalFields;
        case 'general':
            return fieldIsAtTopLevel;
        default:
            return () => true;
    }
};

const filterElectricalFields: FilterFunction = (name, _, values) => {
    const ignoredPrefixes = [
        /^ports\.([0-9]).(AC|DC)\.wireSize/,
        /^ports\.([0-9]).(AC|DC)\.terminal/,
        /^chargeCapacity/,
    ];

    if (ignoredPrefixes.some((regex) => regex.test(name))) {
        return false;
    }

    const match = /^ports\.([0-9]+).(AC|DC)?/.exec(name);

    if (match) {
        if (match[2]) {
            return get(values, `ports.${match[1]}.${match[2]}.enabled`, false);
        }

        return get(values, `ports.${match[1]}.enabled`, false);
    }

    return true;
};

const fieldIsAtTopLevel: FilterFunction = (name: string) => {
    const ignoreFields = [
        'id',
        'type',
        'visibility',
        'reviewed',
        'archivedAt',
        'createdBy',
        'team',
        'sortRank',
        'createdAt',
        'updatedAt',
    ];

    const includeFields = ['compliance', 'lifecycle'];

    if (ignoreFields.includes(name)) {
        return false;
    }

    const isIncudeField = includeFields.some((field) => name.includes(field));

    return isIncudeField || !name.includes('.');
};

export { getFilterFunction };
