import { z } from 'zod';

enum Display {
    NORMAL = '',
    NOT_APPLICABLE = 'na',
    HIDDEN = 'hidden',
    UNKNOWN = 'unknown',
}

const DisplayLabels: Record<Display, string> = {
    [Display.NORMAL]: 'Normal',
    [Display.NOT_APPLICABLE]: 'Not applicable',
    [Display.HIDDEN]: 'Hidden',
    [Display.UNKNOWN]: 'Unknown',
};

const displaySchema = z.enum([Display.NOT_APPLICABLE, Display.UNKNOWN, Display.HIDDEN]);
const notesSchema = z.string();

const fieldMetaSchema = z
    .object({
        display: displaySchema.optional(),
        notes: notesSchema.optional(),
    })
    .passthrough();

const metadataSchema = z.any();

type FieldMeta = z.infer<typeof fieldMetaSchema>;
type Metadata = z.infer<typeof metadataSchema>;

const metadata = metadataSchema.default({});

export { metadata, Display, DisplayLabels };
export type { FieldMeta, Metadata };
