import { z } from 'zod';

import { ComponentValidator, ComponentPayloadValidator } from '../Component';

import { AbstractPort, Port } from '../fields/AbstractPort';

import { ComponentDefinition } from '../../types';

const port = AbstractPort.extend({
    AC: Port.AC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
    DC: Port.DC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
    capacitance: Port.capacitance.validator,
    powerFlowDirection: Port.powerFlowDirection.literals.input,
}).default({});

const HVACBase = z.object({
    type: z.literal('hvac').default('hvac'),
    electrical: z
        .object({
            ports: z.tuple([port]).default([undefined]),
        })
        .default({}),
});

export const HVACValidator = ComponentValidator.merge(HVACBase);
export const HVACPayloadValidator = ComponentPayloadValidator.merge(HVACBase);

export const HVACDefinition: ComponentDefinition = {
    type: 'hvac',
    name: 'HVAC',
    plural: 'HVACs',
    indicator: 'HVAC',
    hasDynamicNumberOfPorts: false,
    ports: {
        min: 1,
        max: Infinity,
        powerFlowDirections: ['input'],
        allowedControlMethods: ['constant-power', 'power-voltage'],
    },
    validators: {
        component: HVACValidator,
        payload: HVACPayloadValidator,
    },
};

export type HVAC = z.infer<typeof HVACValidator>;
export type HVACPayload = z.infer<typeof HVACPayloadValidator>;
