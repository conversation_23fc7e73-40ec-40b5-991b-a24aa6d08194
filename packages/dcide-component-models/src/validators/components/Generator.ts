import { z } from 'zod';

import { ComponentValidator, ComponentPayloadValidator } from '../Component';

import { AbstractPort, Port } from '../fields/AbstractPort';
import { ParallelableCapacity } from '../fields/ParallelableCapacity';

import { ComponentDefinition } from '../../types';

const port = AbstractPort.extend({
    AC: Port.AC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
    DC: Port.DC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
    capacitance: Port.capacitance.validator,
    isolated: Port.isolated.validator,
    parallelableCapacity: ParallelableCapacity.validator,
    powerFlowDirection: Port.powerFlowDirection.literals.output,
}).default({});

const GeneratorBase = z.object({
    type: z.literal('generator').default('generator'),
    electrical: z
        .object({
            ports: z.tuple([port]).default([undefined]),
        })
        .default({}),
});

export const GeneratorValidator = ComponentValidator.merge(GeneratorBase);
export const GeneratorPayloadValidator = ComponentPayloadValidator.merge(GeneratorBase);

export const GeneratorDefinition: ComponentDefinition = {
    type: 'generator',
    name: 'Generator',
    plural: 'Generators',
    indicator: 'G',
    hasDynamicNumberOfPorts: false,
    ports: {
        min: 1,
        max: 1,
        powerFlowDirections: ['output'],
        allowedControlMethods: [
            'constant-power',
            'power-voltage',
            'constant-voltage',
            'constant-voltage-frequency',
            'constant-active-reactive-power',
        ],
    },
    validators: {
        component: GeneratorValidator,
        payload: GeneratorPayloadValidator,
    },
};

export type Generator = z.infer<typeof GeneratorValidator>;
export type GeneratorPayload = z.infer<typeof GeneratorPayloadValidator>;
