import { z } from 'zod';
import { ComponentValidator, ComponentPayloadValidator } from '../Component';
import { IsolationVoltage, i2tRating } from '../fields';

import { AbstractPort, Port } from '../fields/AbstractPort';
import { Current } from '../fields/Current';

import { synchronize, synchronizePowerFlowDirection } from '../helpers';

import { ComponentDefinition } from '../../types';
import { Time } from '../fields/Time';

const extraSpecifications = {
    instantaneousShortCircuitCurrentUL: Current.validators.value,
    serviceShortCircuitBreakingCapacityIEC: Current.validators.value,
    ultimateShortCircuitBreakingCapacityIEC: Current.validators.value,
    minimumBreakingCapacity: Current.validators.value,
};

const port = AbstractPort.extend({
    AC: Port.AC.omit({
        power: true,
        earthingConfigurations: true,
    })
        .extend({
            ...extraSpecifications,
        })
        .default({}),
    DC: Port.DC.omit({
        power: true,
        earthingConfigurations: true,
    })
        .extend({
            ...extraSpecifications,
            maximumTimeConstant: Time.validators.value,
        })
        .default({}),
    numberOfPoles: z.number().positive().default(2),
    overvoltageCategory: z.number().optional(),
}).default({
    powerFlowDirection: 'bidirectional',
});

const electrical = z
    .object({
        ports: z.tuple([port, port]).default([undefined, undefined]),
        standards: z.array(z.string()).default([]),
        isolationVoltage: IsolationVoltage.validator,
        meltingI2t: i2tRating.validator,
        arcingI2t: i2tRating.validator,
        totalClearingI2t: i2tRating.validator,
    })
    .default({});

const FuseValidatorBase = z.object({
    type: z.literal('fuse').default('fuse'),
    electrical: synchronize(synchronizePowerFlowDirection(electrical), {
        'ports.0': ['ports.1'],
    }),
});

export const FuseValidator = ComponentValidator.omit({ communication: true }).merge(FuseValidatorBase);

export const FusePayloadValidator = ComponentPayloadValidator.omit({ communication: true }).merge(FuseValidatorBase);

export const FuseDefinition: ComponentDefinition = {
    type: 'fuse',
    name: 'Fuse',
    plural: 'Fuses',
    indicator: 'F',
    hasDynamicNumberOfPorts: false,
    ports: {
        min: 2,
        max: 2,
        powerFlowDirections: ['input', 'output', 'bidirectional'],
        displayAsSinglePort: true,
    },
    validators: {
        component: FuseValidator,
        payload: FusePayloadValidator,
    },
};

export type Fuse = z.infer<typeof FuseValidator>;
export type FusePayload = z.infer<typeof FusePayloadValidator>;
