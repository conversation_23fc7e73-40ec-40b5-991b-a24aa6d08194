import { z } from 'zod';

import { AbstractMeasurement } from './AbstractMeasurement';

const units = ['s'] as const;
const unit = z.literal('s').default('s');

const validator = AbstractMeasurement.value
    .extend({
        unit,
    })
    .default({});

const valueValidator = AbstractMeasurement.value
    .extend({
        unit,
    })
    .default({});

export const Time = {
    validator,
    validators: {
        value: valueValidator,
        unit,
    },
    units,
};

export type Time = z.infer<typeof validator>;
