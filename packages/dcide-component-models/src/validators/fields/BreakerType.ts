import { z } from 'zod';

import { Option } from '../../types';

export enum BreakerTypes {
    MCB = 'MCB',
    MCCB = 'MCCB',
    SSCB = 'SSCB',
}

const options: Option<BreakerTypes>[] = [
    { value: BreakerTypes.MCB, label: 'MCB (Miniature Circuit Breaker)' },
    { value: BreakerTypes.MCCB, label: 'MCCB (Molded Case Circuit Breaker)' },
    { value: BreakerTypes.SSCB, label: 'SSCB (Solid-State Circuit Breaker)' },
];

const validator = z.nativeEnum(BreakerTypes);

export const BreakerType = {
    validator,
    options,
};
