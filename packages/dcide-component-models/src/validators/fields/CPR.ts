import { z } from 'zod';

export enum CPRClass {
    Aca = 'Aca',
    B1ca = 'B1ca',
    B2ca = 'B2ca',
    Cca = 'Cca',
    Dca = 'Dca',
    Eca = 'Eca',
    Fca = 'Fca',
}

export enum CPRSmokeProduction {
    S1 = 's1',
    S1a = 's1a',
    S1b = 's1b',
    S2 = 's2',
    S3 = 's3',
}

export enum CPRFlamingDroplets {
    D0 = 'd0',
    D1 = 'd1',
    D2 = 'd2',
}

export enum CPRAcidity {
    A1 = 'a1',
    A2 = 'a2',
    A3 = 'a3',
}

export const CPRClassValidator = z.nativeEnum(CPRClass);
export const CPRSmokeProductionValidator = z.nativeEnum(CPRSmokeProduction);
export const CPRFlamingDropletsValidator = z.nativeEnum(CPRFlamingDroplets);
export const CPRAcidityValidator = z.nativeEnum(CPRAcidity);

export const CPRValidator = z
    .object({
        class: CPRClassValidator.nullable().default(null),
        smokeProduction: CPRSmokeProductionValidator.nullable().default(null),
        flamingDroplets: CPRFlamingDropletsValidator.nullable().default(null),
        acidity: CPRAcidityValidator.nullable().default(null),
    })
    .default({});

export type CPR = z.infer<typeof CPRValidator>;
