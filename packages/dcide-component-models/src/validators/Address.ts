import { z } from 'zod';

const AddressSchema = z.object({
    name: z.string().default(''),
    street: z.string().default(''),
    number: z.string().default(''),
    postalCode: z.string().default(''),
    city: z.string().default(''),
    state: z.string().default(''),
    country: z.string().default(''),
    coordinates: z.tuple([z.number(), z.number()]).optional(),
});

export { AddressSchema };
