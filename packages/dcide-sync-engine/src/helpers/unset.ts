import { set } from './set';

export const unset = <T extends object>(target: T, path: string): T => {
    const pathArray = path.split('.');

    let returnValue: any = target;

    pathArray.forEach((key, index) => {
        if (index === pathArray.length - 1) {
            if (Array.isArray(returnValue)) {
                const _returnValue: any[] = returnValue;

                const index = key.match(/^\d+$/) ? parseInt(key) : _returnValue.findIndex((item) => item?.id === key);

                if (index !== -1) {
                    const path = pathArray.slice(0, -1).join('.');
                    set(
                        target,
                        path,
                        _returnValue.filter((_, i) => i !== index),
                    );
                }
            } else {
                delete returnValue[key];
            }
        } else {
            if (Array.isArray(returnValue)) {
                const _returnValue: any[] = returnValue;

                if (key.match(/^\d+$/)) {
                    returnValue = _returnValue[parseInt(key)];
                } else {
                    returnValue = _returnValue.find((item) => item?.id === key);
                }
            } else {
                if (returnValue[key] === undefined) {
                    return target;
                }

                returnValue = returnValue[key];
            }
        }
    });

    return target;
};
