{"name": "dcide", "workspaces": ["apps/*", "packages/*"], "scripts": {"test:unit": "turbo run test:unit", "build": "turbo run build", "deploy": "turbo run deploy", "format": "biome format --write", "dev": "turbo run dev", "lint": "turbo run lint", "prepare": "husky", "patch": "patch-package --patch-dir=./patches"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/cookie": "^0.6.0", "@types/jest": "30.0.0", "@types/node": "24.0.3", "husky": "^9.0.11", "jest": "30.0.1", "lint-staged": "^14.0.1", "ts-jest": "29.4.0", "ts-node": "^10.9.2", "turbo": "^1.13.0", "typescript": "5.8.3", "stripe": "18.2.1"}, "dependencies": {"@sentry/nextjs": "9.30.0", "@sentry/react": "9.30.0", "@tiptap/html": "2.14.0", "ably": "2.9.0", "cookie": "1.0.2", "fuse.js": "^7.1.0", "next": "15.3.1", "radash": "12.1.1", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^2.12.7", "stripe": "^15.2.0", "zod": "3.25.67", "zod-to-json-schema": "^3.24.1"}}