import React from 'react';

import { EmailCopyHelpers } from '../../helpers/EmailCopyHelpers';
import { General, EmailTemplateProps } from '../../templates/General';

const Template = (props: EmailTemplateProps) => <General {...props} showSubject />;

export const copy = EmailCopyHelpers.companyAddedAsPartner({
    company: {
        id: 'xxx',
        name: 'CE+T',
        slug: 'ce-t',
    },
    addedByCompany: {
        id: 'xxx',
        name: 'Direct Energy Partners',
        slug: 'direct-energy-partners',
    },
    acceptUrl: '#',
    addedByUrl: '#',
});

Template.PreviewProps = copy;

export default Template;
