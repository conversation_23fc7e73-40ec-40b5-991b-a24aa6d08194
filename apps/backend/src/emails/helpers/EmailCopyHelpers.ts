import { Component, Manufacturer, Project, Team, User } from '@/payload-types';

import { EmailTemplateProps } from '../templates/General';

import { EmailCopyHelpersUser } from './EmailCopyHelpers.User';
import { EmailCopyHelpersCompany } from './EmailCopyHelpers.Company';
import { EmailCopyHelpersDiagram } from './EmailCopyHelpers.Diagram';
import { EmailCopyHelpersSubscription } from './EmailCopyHelpers.Subscription';
import { EmailCopyHelpersTeam } from './EmailCopyHelpers.Team';

export type EmailCopyBaseProps = {
    subject: string;
    buttonUrl: string;
    linkUrl?: string;
    loginCode: string;
    showIntro: boolean;
    company: Pick<Manufacturer, 'id' | 'name' | 'slug'>;
    user: Pick<User, 'id' | 'name' | 'email' | 'signup' | 'profileImage'>;
    project: Pick<Project, 'id' | 'name'>;
    component: Pick<Component, 'id' | 'name'>;
    team: Pick<Team, 'id' | 'name'>;
} & Pick<EmailTemplateProps, 'title' | 'text' | 'message' | 'button'>;

const EmailCopyHelpers = {
    ...EmailCopyHelpersUser,
    ...EmailCopyHelpersCompany,
    ...EmailCopyHelpersDiagram,
    ...EmailCopyHelpersSubscription,
    ...EmailCopyHelpersTeam,
};

export { EmailCopyHelpers };
