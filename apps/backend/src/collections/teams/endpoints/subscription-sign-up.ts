import type { Endpoint } from 'payload';
import { z } from 'zod';

import { ForbiddenError } from '../../../responses/errors';
import { AccessHelpers, getAuthenticatedRequestUser, handleEndpointError } from '../../../helpers';
import {
    DesignerSubscription,
    CompanySubscription,
    SubscriptionBillingCycle,
    isDesignerSubscription,
} from '@repo/dcide-component-models';
import { StripeService } from '../../../services/StripeService';

import { sendSubscriptionUpdateEmail } from '@/helpers/send-subscription-update-email';
import { performPostSubscribeActions } from './post-create-subscription';

const subscriptionSignUp: Endpoint = {
    path: '/:teamId/subscription-sign-up',
    method: 'post',
    handler: async (request) => {
        try {
            const user = await getAuthenticatedRequestUser(request);
            const team = await request.payload.findByID({
                collection: 'teams',
                id: request.routeParams?.teamId as string,
            });

            if (!AccessHelpers.userIsTeamOwner(user, team)) {
                throw new ForbiddenError();
            }

            const {
                subscription,
                billingCycle,
                numberOfSeats,
                additionalSubscriptions = [],
                interestedIn = [],
            } = z
                .object({
                    subscription: z.union([z.nativeEnum(DesignerSubscription), z.nativeEnum(CompanySubscription)]),
                    billingCycle: z.nativeEnum(SubscriptionBillingCycle),
                    numberOfSeats: z.number().min(team.users.length),
                    additionalSubscriptions: z
                        .union([z.nativeEnum(DesignerSubscription), z.nativeEnum(CompanySubscription)])
                        .array()
                        .optional(),
                    interestedIn: z.string().array().optional(),
                })
                .parse(await request.json!());

            const stripeCustomerId = await StripeService.createOrGetStripeCustomerId(user, team);

            const allSubscriptions = [subscription, ...additionalSubscriptions].map((sub) => {
                return {
                    subscription: sub,
                    // If the subscription is a designer subscription, use the number of seats provided, otherwise use 1
                    seats: isDesignerSubscription(sub) ? numberOfSeats : 1,
                };
            });

            const result = await StripeService.createOrUpdateSubscriptions(
                stripeCustomerId,
                allSubscriptions,
                billingCycle,
            );

            await request.payload.update({
                collection: 'teams',
                id: team.id,
                data: {
                    interestedIn: interestedIn.join(', '),
                },
            });

            if (result.action === 'created') {
                return Response.json({ redirect: result.redirect });
            }

            if (result.action === 'updated') {
                await performPostSubscribeActions(request, user, team, subscription, additionalSubscriptions);

                const subscriptionDetails = await StripeService.getCustomerSubscription(stripeCustomerId);
                const subscriptionSchedule = await StripeService.getCustomerSubscriptionSchedule(
                    stripeCustomerId,
                    subscriptionDetails.id,
                );

                if (!subscriptionSchedule) {
                    throw new Error(
                        `Subscription schedule not found after update for team ID: ${team.id},` +
                            ` subscription ID: ${subscriptionDetails.id}`,
                    );
                }

                const updatedTeam = await StripeService.syncSubscription(
                    team.id,
                    subscriptionDetails,
                    subscriptionSchedule,
                );

                await sendSubscriptionUpdateEmail(subscriptionDetails, subscriptionSchedule, user);

                return Response.json({ updatedTeam });
            }

            throw new Error(`Unreachable code. createOrUpdateSubscriptions result: ${JSON.stringify(result)}`);
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { subscriptionSignUp };
