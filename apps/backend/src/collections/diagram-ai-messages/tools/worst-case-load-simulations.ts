import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

import { PayloadService } from '@/services/PayloadService';
import { getId } from '@/helpers';
import { pick } from 'radash';

const name = 'worstCaseLoadSimulations';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: `
        Retrieves information about the latest worst case load simulation for this diagram.
        Possible responses are:
        * The latest simulation with a status and results
        * A message indicating that no simulations have been run for this diagram
    `,
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {
    newWorstCaseLoadSimulation: {
        name: 'newWorstCaseLoadSimulation',
        action: () => `
            === ACTION ===
            {
                "action": "newWorstCaseLoadSimulation"
            }
            === END ACTION ===
        `,
    },
    showWorstCaseLoadSimulation: {
        name: 'showWorstCaseLoadSimulation',
        action: (simulationId: string) => `
            === ACTION ===
            {
                "action": "showWorstCaseLoadSimulation",
                "simulationId": "${simulationId}"
            }
            === END ACTION ===
        `,
    },
} as const;

const handler = async (message: DiagramAIMessage) => {
    const payload = await PayloadService.getPayload();
    const {
        docs: [simulation],
    } = await payload.find({
        collection: 'wireSizings',
        where: {
            diagram: {
                equals: getId(message.diagram),
            },
        },
        depth: 0,
        limit: 1,
        sort: '-createdAt',
    });

    if (simulation) {
        return JSON.stringify(pick(simulation, ['id', 'name', 'status', 'error', 'result']));
    }

    return 'There are no worst case load simulations for this diagram.';
};

const worstCaseLoadSimulations = {
    name,
    feedback: 'Consulting your worst case load simulations',
    definition,
    actions,
    handler,
};

export { worstCaseLoadSimulations };
