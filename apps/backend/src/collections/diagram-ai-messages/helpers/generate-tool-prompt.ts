const generateToolPrompt = ({
    name,
    objective,
    activationCondition,
    exampleQuestions = [],
    toolCalling,
    mandatoryToolCalling,
    handler,
    notes = [],
}: {
    name: string;
    objective: string;
    activationCondition?: string;
    exampleQuestions?: string[];
    toolCalling?: string;
    mandatoryToolCalling?: string;
    handler: string;
    notes?: {
        title: string;
        description?: string;
    }[];
}) => {
    /**
     * It's important that all the sections have the same indentation..
     * For the AI-system but mostly for Kobe's autism
     * (Wrongly indented blocks will render as a code block)
     */

    const sections: string[] = [];

    if (name && objective) {
        sections.push(`
            ## ${name}

            **Objective:**
            ${trim(objective)}
        `);
    }

    if (activationCondition) {
        sections.push(`
            **Activation Condition:**
            ${trim(activationCondition)}
        `);
    }

    if (exampleQuestions.length) {
        const lines = exampleQuestions.map((question) => {
            return `* "${question}"`;
        });

        sections.push(`
            **Example Questions:**
            ${lines.join('\n')}
        `);
    }

    if (toolCalling) {
        sections.push(`
            **Tool Calling:**
            ${trim(toolCalling)}
        `);
    }

    if (mandatoryToolCalling) {
        sections.push(`
            **Mandatory Tool Calling:**
            ${trim(mandatoryToolCalling)}
        `);
    }

    if (handler) {
        sections.push(`
            **Handling the User's Question:**
            ${trim(handler)}
        `);
    }

    if (notes.length) {
        const lines = notes.map((note) => {
            return trim(`
                *   ${note.title}${note.description ? ':' : ''}
                    ${note.description}
            `);
        });

        sections.push(`
            **Notes:**
            ${lines.join('\n')}
        `);
    }

    return sections.map((section) => trim(section)).join('\n\n');
};

const trim = (stringOrLiteral: string) => {
    const lines = stringOrLiteral.split('\n');

    // Delete first line if it's empty
    if (lines[0] !== undefined && !lines[0].trim()) {
        delete lines[0];
    }

    // Delete last line if it's empty
    if (lines[lines.length - 1] !== undefined && !lines[lines.length - 1].trim()) {
        delete lines[lines.length - 1];
    }

    const remaining = lines.filter((line) => line !== null);
    const first = remaining[0];

    let indentation = 0;

    if (first) {
        const characters = first.split('');
        let found = false;

        for (let i = 0; i < characters.length && !found; i++) {
            if (characters[i] === ' ') {
                indentation++;
            } else {
                found = true;
            }
        }
    }

    const whitespaceToRemove: string[] = [];

    for (let i = 0; i < indentation; i++) {
        whitespaceToRemove.push(' ');
    }

    const removeIndentation = (string: string) => {
        return string.replace(whitespaceToRemove.join(''), '');
    };

    return remaining
        .map(removeIndentation)
        .map((line) => line.trimEnd())
        .join('\n');
};

export { generateToolPrompt };
