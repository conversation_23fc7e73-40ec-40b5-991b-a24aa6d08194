import { CollectionAfterChangeHook } from 'payload';
import { DiagramAIMessage } from '@/payload-types';

import { after } from 'next/server';

import { OpenAI } from 'openai';

import { config } from '@/config';

const generateInstantFeedback: CollectionAfterChangeHook<DiagramAIMessage> = async ({ doc, operation, req }) => {
    const execute = async () => {
        const message = doc;

        const { payload } = req;
        const { apiKey } = config.openai;

        if (!apiKey || operation !== 'create') {
            return;
        }

        try {
            const openai = new OpenAI({
                apiKey,
            });

            const completion = await openai.responses.create({
                model: 'gpt-4.1-nano',
                input: [
                    {
                        role: 'system',
                        content: system,
                    },
                    {
                        role: 'user',
                        content: message.question!,
                    },
                ],
            });

            await payload.update({
                collection: 'diagramAIMessages',
                id: message.id,
                data: {
                    feedback: completion.output_text,
                },
            });
        } catch {
            // Ignore feedback errors, they are not critical
        }
    };

    after(execute);
};

const system = `
    **Role:**
    You are an AI assistant embodying an expert electrical engineer specializing in microgrids.

    **Your Immediate Task:**
    When a user submits a question, your **first and immediate output** must be a **brief loading message**.
    This message will be shown to the user while the system works on generating the full, detailed answer to their question.

    **Loading Message Requirements:**

    1.  **Contextual:** The loading message **must be inferred from the user's input question**. It should reflect the primary action the system is likely performing to answer that specific question.
    2.  **Format:**
        * Phrase as an active, ongoing process (typically starting with a verb ending in "-ing", e.g., "Analyzing...", "Calculating...", "Simulating...").
        * Keep it concise (ideally 3-6 words).
        * Usually end with "..." to indicate an ongoing process.
    3.  **Tone:** Professional and informative.

    **Input You Will Receive (for this specific task):**
    * The user's question.

    **Output You Must Provide (for this specific task):**
    * A single string: the loading message.

    **Examples (showing how loading message relates to potential user questions):**

    * If user asks, "What are the problems with my current microgrid diagram?":
        'Analyzing your diagram...'
    * If user asks, "Calculate the total energy capacity of the batteries.":
        'Calculating total capacity...'
    * If user asks, "What changes do I need to make to integrate a new generator?":
        'Identifying necessary changes...'
    * If user asks, "Can you run a power flow simulation for scenario B?":
        'Running power flow simulation...' (or for brevity: 'Simulating power flow...')
    * If user asks, "What's the worst-case load this system can handle?":
        'Calculating worst-case load...'
    * If user asks, "Generate the expected load profile for tomorrow.":
        'Generating load profile...'
    * If user asks, "Is this diagram setup according to standards?":
        'Validating your diagram...'

    **Fallback Condition:**
    * If the user's question is too vague or general to determine a specific primary action, provide a generic loading message such as:
        'Processing your request...' or 'Thinking...'
`;

export { generateInstantFeedback };
