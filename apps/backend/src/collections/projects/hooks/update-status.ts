import { alphabetical } from 'radash';

import { Project } from '@/payload-types';
import { CollectionBeforeChangeHook } from 'payload';

const updateStatus: CollectionBeforeChangeHook = async ({ data }) => {
    const { statusUpdates } = data as Partial<Project>;

    if (!statusUpdates) return data;

    const lastStatusUpdate = alphabetical(statusUpdates, (statusUpdate) => statusUpdate.date ?? '', 'desc')[0];

    if (!lastStatusUpdate) return data;

    return {
        ...data,
        status: lastStatusUpdate.status,
    };
};

export { updateStatus };
