import type { Endpoint } from 'payload';

const editCollaborator: Endpoint = {
    path: '/:project/collaborators/edit',
    method: 'post',
    handler: async (request) => {
        const payload = request.payload;

        const params = request.routeParams as {
            project: string;
        };

        // todo, use <PERSON>od for object validation
        const body = (await request.json!()) as {
            collaborator: string;
            permissions: ('project.view' | 'project.edit')[];
        };

        const project = await payload.findByID({
            collection: 'projects',
            id: params.project,
            depth: 0,
        });

        const users = project.collaborators?.users?.map((needle) => {
            return needle.user === body.collaborator
                ? {
                      user: needle.user,
                      permissions: body.permissions,
                  }
                : needle;
        });

        const updatedProject = await payload.update({
            collection: 'projects',
            id: project.id,
            data: {
                collaborators: {
                    ...project.collaborators,
                    users,
                },
            },
            depth: 0,
        });

        return Response.json(updatedProject);
    },
};

export { editCollaborator };
