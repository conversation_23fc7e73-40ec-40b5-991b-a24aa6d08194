import { getAuthenticatedRequestUser } from '@/helpers';
import type { Endpoint } from 'payload';

type Status = 'progress' | 'review' | 'done' | 'archived';

const editStatus: Endpoint = {
    path: '/:projectId/status/edit',
    method: 'patch',
    handler: async (request) => {
        const user = await getAuthenticatedRequestUser(request);
        const payload = request.payload;

        const { projectId } = request.routeParams as {
            projectId: string;
        };

        const { status } = (await request.json!()) as { status: Status };

        const project = await payload.findByID({
            collection: 'projects',
            id: projectId,
            depth: 0,
        });

        const statusUpdates = project.statusUpdates ?? [];

        statusUpdates.push({
            status,
            user: user.id,
            date: new Date().toISOString(),
        });

        const newProject = await payload.update({
            collection: 'projects',
            id: project.id,
            data: {
                statusUpdates,
            },
        });

        return Response.json({
            success: true,
            doc: newProject,
        });
    },
};

export { editStatus };
