import { CollectionBeforeChangeHook } from 'payload';
import { Simulation, WireSizing } from '@/payload-types';

import { JobStatus } from '@repo/dcide-component-models';

import { getCollection } from '../../../helpers/get-collection';
import { pick } from 'radash';

const generateDiagramSnapshot: CollectionBeforeChangeHook<Simulation | WireSizing> = async ({
    data,
    operation,
    originalDoc,
}) => {
    const statusChangedToPending = originalDoc?.status !== JobStatus.PENDING && data?.status === JobStatus.PENDING;

    if ((operation === 'create' || statusChangedToPending) && data.diagram) {
        const diagram = await getCollection('projectDesignDiagrams', data.diagram);
        const diagramSnapshot = pick(diagram, ['id', 'componentInstances', 'connections', 'groups', 'textareas']);

        return {
            ...data,
            diagramSnapshot,
        };
    }
};

export { generateDiagramSnapshot };
