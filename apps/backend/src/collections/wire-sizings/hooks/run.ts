import { WireSizing } from '@/payload-types';
import { SimulationService } from '../../../services/SimulationService';

import { JobHelpers } from '../../jobs/JobHelpers';

const run = JobHelpers.createRunner('wireSizings', async (doc: WireSizing, isAsync: boolean) => {
    return await SimulationService.sendWireSizingRequest({
        ...(isAsync && { id: doc.id }),
        diagram: doc.diagramSnapshot as any,
    });
});

export { run };
