import type { Endpoint, PayloadRequest } from 'payload';
import { DiagramConnection, DiagramConnectionSchema } from '@repo/dcide-component-models';

import { z } from 'zod';
import { config } from '@/config';

const calculateWireSize: Endpoint = {
    path: '/calculate-wire-size',
    method: 'post',
    handler: async (request: PayloadRequest) => {
        const { standard, voltageDrop, voltageType, connection } = z
            .object({
                standard: z.enum(['IEC', 'NEC']),
                voltageDrop: z.number().min(0).max(1),
                voltageType: z.enum(['AC', 'DC']).nullable(),
                connection: DiagramConnectionSchema,
            })
            .parse(await request.json!());

        const lines: DiagramConnection['lines'] = {
            AC: {
                L1: false,
                L2: false,
                L3: false,
                N: false,
                PE: false,
            },
            DC: {
                'L+': false,
                'M': false,
                'L-': false,
                'PE': false,
            },
        };

        if (voltageType === 'AC') {
            lines.AC = connection.lines.AC;
        }

        if (voltageType === 'DC') {
            lines.DC = connection.lines.DC;
        }

        const response = await fetch(config.wireSizing.endpoint, {
            method: 'POST',
            body: JSON.stringify({
                method: 'GET_WIRE_SIZE',
                standard,
                voltageDrop,
                connections: {
                    [connection.id]: {
                        ...connection,
                        lines,
                        requirements: {
                            voltage: connection.requirements.voltage.value,
                            current: {
                                'L+': connection.requirements.current['L+'].value,
                                'L-': connection.requirements.current['L-'].value,
                                'PE': connection.requirements.current['PE'].value,
                            },
                        },
                    },
                },
            }),
        });

        return Response.json(await response.json());
    },
};

export { calculateWireSize };
