import { CollectionBeforeChangeHook } from 'payload';

import { getRequestTeam } from '../../../helpers/get-request-team';

const addTeamVoltageClasses: CollectionBeforeChangeHook = async ({ req, data, operation }) => {
    if (operation === 'create') {
        const team = await getRequestTeam(req);

        if (team) {
            data.voltageClasses = team.voltageClasses;
        }

        return data;
    }
};

export { addTeamVoltageClasses };
