import { CollectionConfig } from 'payload';

import { CreatedBy } from '@/fields/metadata/CreatedBy';
import { Diagram } from '@/fields/Diagram';
import { Project } from '@/fields/Project';

const DiagramSimulationProfiles: CollectionConfig = {
    slug: 'diagramSimulationProfiles',
    labels: {
        singular: 'Diagram Simulation Profile',
        plural: 'Diagram Simulation Profiles',
    },
    access: {},
    fields: [
        {
            name: 'name',
            type: 'text',
        },
        {
            name: 'type',
            type: 'select',
            options: [
                { value: 'load', label: 'Load' },
                { value: 'generation', label: 'Generation' },
                { value: 'combined', label: 'Combined' },
            ],
            defaultValue: 'combined',
        },
        {
            name: 'data',
            type: 'textarea',
        },
        Project,
        Diagram,
        CreatedBy,
    ],
};

export default DiagramSimulationProfiles;
