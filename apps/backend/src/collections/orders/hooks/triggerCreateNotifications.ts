import { CollectionAfterChangeHook } from 'payload';

import { Order } from '@/payload-types';

import { getId } from '../../../helpers/get-id';
import { NotificationService } from '../../../services/NotificationService';
import { NotificationKey } from '@repo/dcide-component-models';

const triggerCreateNotifications: CollectionAfterChangeHook<Order> = ({ req, doc, operation }) => {
    if (!req.user) return;
    if (operation !== 'create') return;

    // don't wait up
    NotificationService.create({
        team: getId(doc.team),
        from: req.user.id,
        to: doc.subscribers?.map((subscriber) => getId(subscriber)) ?? [],
        notification: {
            type: NotificationKey.ORDER_EVENT,
            data: {
                orderId: doc.id,
                status: doc.status ?? 'draft',
            },
        },
    });
};

export { triggerCreateNotifications };
