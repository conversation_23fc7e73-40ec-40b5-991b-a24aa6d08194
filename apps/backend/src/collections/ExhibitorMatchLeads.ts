import { CollectionConfig } from 'payload';

import { RTEField } from '@/fields/RichTextEditor';

import { RequestAccess } from '../access/RequestAccess';

import { convertEmailToUser } from '@/collections/exhibitor-match-leads/hooks/convert-email-to-user';
import { updateExhibitorMatch } from '@/collections/exhibitor-match-leads/hooks/update-exhibitor-match';
import { notifyAdminsOnNewLead } from '@/collections/exhibitor-match-leads/hooks/notify-admins-on-new-lead';

const ExhibitorMatchLeads: CollectionConfig = {
    slug: 'exhibitorMatchLeads',
    access: {
        create: RequestAccess.userIsDeveloper,
        read: () => true,
        update: RequestAccess.userIsDeveloper,
        delete: RequestAccess.userIsDeveloper,
    },
    fields: [
        {
            name: 'exhibitorMatch',
            type: 'relationship',
            relationTo: 'exhibitorMatches',
            required: true,
            maxDepth: 0,
        },
        {
            name: 'company',
            type: 'relationship',
            relationTo: 'manufacturers',
            required: true,
            maxDepth: 0,
        },
        RTEField({ name: 'requirements' }),
        {
            name: 'email',
            type: 'email',
        },
        {
            name: 'user',
            type: 'relationship',
            relationTo: 'users',
            maxDepth: 0,
        },
        {
            name: 'event',
            type: 'relationship',
            relationTo: 'events',
            maxDepth: 0,
        },
    ],
    hooks: {
        beforeChange: [convertEmailToUser],
        afterChange: [updateExhibitorMatch, notifyAdminsOnNewLead],
    },
};

export default ExhibitorMatchLeads;
