import { CollectionBeforeValidateHook } from 'payload';
import { Component } from '@/payload-types';
import { cleanObject } from '@repo/dcide-component-models';

const cleanData: CollectionBeforeValidateHook<Component> = ({ data }) => {
    if (!data) return;

    cleanMetadata(data);

    return data;
};

const cleanMetadata = (data: Partial<Component>) => {
    if (!('metadata' in data) || typeof data.metadata !== 'object' || !data.metadata) return;

    data.metadata = cleanObject(data.metadata, { removeEmpty: true }) as Record<string, unknown>;
};

export { cleanData };
