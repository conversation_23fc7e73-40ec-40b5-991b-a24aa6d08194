import { File, ComponentVisibility } from '@repo/dcide-component-models';
import type { Endpoint, Payload } from 'payload';

import { getId } from '../../../helpers/get-id';
import { getRequestUser } from '../../../helpers/get-request-user';

const files: Endpoint = {
    path: '/:componentId/files',
    method: 'get',
    handler: async (request) => {
        const payload = request.payload;
        const componentId = request.routeParams?.componentId as string;
        const user = await getRequestUser(request);

        let isPrivate = ComponentVisibility.PRIVATE in request.query;
        let isPublic = ComponentVisibility.PUBLIC in request.query;
        const results: File[] = [];

        const shouldReturnAll = !isPrivate && !isPublic;
        if (shouldReturnAll) {
            isPrivate = true;
            isPublic = true;
        }

        if (isPublic) {
            const publicComponentFiles = await getPublicComponentFiles(payload, componentId);
            results.push(...publicComponentFiles);
        }

        if (isPrivate && user) {
            const privateComponentFiles = await getPrivateComponentFiles(payload, componentId, getId(user.team));
            results.push(...privateComponentFiles);
        }

        return Response.json(results);
    },
};

const getPublicComponentFiles = async (payload: Payload, componentId: string) => {
    const { files: publicComponentFilesMap } = await payload.findByID({
        collection: 'components',
        id: componentId,
        depth: 0,
    });

    const publicFileIds = publicComponentFilesMap?.map((file: any) => file.file);
    const { docs: publicComponentFiles } = await payload.find({
        collection: 'files',
        where: {
            id: {
                in: publicFileIds,
            },
        },
        depth: 0,
        limit: 100,
    });

    return (
        publicComponentFilesMap?.map((file: any) => ({
            ...file,
            file: publicComponentFiles.find((publicFile: any) => publicFile.id === file.file),
        })) ?? []
    );
};

const getPrivateComponentFiles = async (payload: Payload, componentId: string, teamId: string) => {
    const { docs: privateComponentFilesMap } = await payload.find({
        collection: 'componentFiles',
        where: {
            component: {
                equals: componentId,
            },
            team: {
                equals: teamId,
            },
        },
        sort: 'createdAt',
        depth: 0,
        limit: 100,
    });
    const privateFileIds = privateComponentFilesMap.map((file: any) => file.file);
    const { docs: privateComponentFiles } = await payload.find({
        collection: 'files',
        where: {
            id: {
                in: privateFileIds,
            },
        },
        depth: 0,
        limit: 100,
    });

    return privateComponentFilesMap.map((file: any) => ({
        ...file,
        file: privateComponentFiles.find((privateFile: any) => privateFile.id === file.file),
    }));
};

export { files };
