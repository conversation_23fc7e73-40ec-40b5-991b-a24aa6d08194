import { JsonSuccessResponse } from '@/responses/JsonSuccessResponse';
import { handleEndpointError } from '../../../helpers';
import { EmailService } from '../../../services';
import { User } from '@/payload-types';
import { config } from '@/config';

import type { Endpoint } from 'payload';

const changeReviewStatus: Endpoint = {
    path: '/:id/change-review-status',
    method: 'post',
    handler: async (request) => {
        try {
            const component = await request.payload.findByID({
                collection: 'components',
                id: request.routeParams?.id as string,
                depth: 1,
            });

            const { email } = component.createdBy as User;

            await request.payload.update({
                collection: 'components',
                id: component.id,
                data: {
                    reviewed: true,
                },
            });

            await EmailService.send({
                to: email,
                data: EmailService.copy.productPublish({
                    component,
                    buttonUrl: `${config.urls.frontend}/products/${component.id}`,
                }),
            });

            return JsonSuccessResponse();
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { changeReviewStatus };
