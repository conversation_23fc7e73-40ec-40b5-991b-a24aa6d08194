import { handleEndpointError } from '../../../helpers/handle-endpoint-error';
import type { Endpoint } from 'payload';
import { getRequestUser } from '../../../helpers/get-request-user';
import { getId } from '../../../helpers/get-id';
import { Where } from 'payload';

const getProductSeriesComponents: Endpoint = {
    path: '/product-series/:manufacturerId/:productSeries',
    method: 'get',
    handler: async (request) => {
        try {
            const payload = request.payload;
            const user = await getRequestUser(request);

            const manufacturerId = request.routeParams?.['manufacturerId'];
            const dirtyProductSeries = request.routeParams?.['productSeries'] as string;
            const productSeries = dirtyProductSeries.replace('++--++', '/');

            const conditions: Where['or'] = [{ visibility: { equals: 'public' } }];

            if (user) {
                conditions.push({ team: { equals: getId(user.team) } });
            }

            const result = await payload.find({
                collection: 'components',
                where: {
                    productSeries: { equals: productSeries },
                    manufacturer: { equals: manufacturerId },
                    or: conditions,
                },
                limit: 9999,
                depth: 0,
            });

            return Response.json(
                user
                    ? result.docs
                    : result.docs.map(({ id, name, productIdentifier }) => ({ id, name, productIdentifier })),
            );
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { getProductSeriesComponents };
