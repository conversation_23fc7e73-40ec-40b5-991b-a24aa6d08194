import { Component } from '@/payload-types';

import { getPayload } from './get-payload';

const savePorts = async (data: Partial<Component>) => {
    const payload = await getPayload();

    const ports =
        typeof data.electrical === 'object' &&
        data.electrical !== null &&
        'ports' in data.electrical &&
        Array.isArray(data.electrical.ports)
            ? data.electrical.ports
            : null;

    if (!ports) {
        return;
    }

    await payload.delete({
        collection: 'componentPorts',
        where: {
            component: { equals: data.id },
        },
    });

    await Promise.all(
        ports.map((port, index) =>
            payload.create({
                collection: 'componentPorts',
                data: {
                    component: data.id!,
                    type: data.type!,
                    index,
                    data: port,
                },
            }),
        ),
    );
};

export { savePorts };
