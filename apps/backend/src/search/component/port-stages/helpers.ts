import { MinNomMax } from './types';

export const getPortMatchKey = (index: number) => `port${index}Match`;
export const getPortMatchIndexKey = (index: number) => `${getPortMatchKey(index)}Index`;

export const multiplyMinNomMax = (base: MinNomMax, multiplier: MinNomMax) => {
    const multiplyIfDefinedInBoth = (key: keyof MinNomMax) =>
        base[key] !== undefined && multiplier[key] !== undefined ? base[key] * multiplier[key] : undefined;

    return {
        min: multiplyIfDefinedInBoth('min'),
        nom: multiplyIfDefinedInBoth('nom'),
        max: multiplyIfDefinedInBoth('max'),
    };
};

export const divideMinNomMax = (base: MinNomMax, multiplier: MinNomMax) => {
    const divideIfDefinedInBoth = (key: keyof MinNomMax) =>
        base[key] !== undefined && multiplier[key] !== undefined ? base[key] / multiplier[key] : undefined;

    return {
        min: divideIfDefinedInBoth('min'),
        nom: divideIfDefinedInBoth('nom'),
        max: divideIfDefinedInBoth('max'),
    };
};
