import { Types, PipelineStage } from 'mongoose';
import { ComponentSearchState } from '@/search/types';
import { ComponentVisibility, PublishedStatus } from '@repo/dcide-component-models';

export const addPermissionsFilters = (search: ComponentSearchState): void => {
    search.stages.push(notDeletedFilter);

    search.stages.push(...getPermissionStages(search));
};

const notDeletedFilter: PipelineStage = { $match: { deletedAt: null } };

const getPermissionStages = (search: ComponentSearchState): PipelineStage[] => {
    const hasTeam = Boolean(search.teamId);

    if (!hasTeam) {
        return [isPublicFilter];
    }

    const teamObjectId = new Types.ObjectId(search.teamId);

    return [isNotTeamComponentFilter, isOwnedByTeamOrPublic(teamObjectId)];
};

const publicCondition = {
    visibility: ComponentVisibility.PUBLIC,
    ['manufacturerDetails.status']: PublishedStatus.PUBLISHED,
};

const isPublicFilter: PipelineStage = { $match: publicCondition };

const isNotTeamComponentFilter: PipelineStage = { $match: { manufacturer: { $ne: null } } };

const isOwnedByTeamOrPublic = (teamId: Types.ObjectId): PipelineStage => ({
    $match: {
        $or: [
            publicCondition,
            {
                team: teamId,
            },
        ],
    },
});
