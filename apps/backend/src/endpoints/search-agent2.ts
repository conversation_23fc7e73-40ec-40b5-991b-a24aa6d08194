import type { Endpoint, Payload, PayloadRequest } from 'payload';

import OpenAI from 'openai';

import { z } from 'zod';

import { AIService, SlackService } from '@/services';
import { SearchService } from '@/services/search/SearchService';
import { config } from '@/config';
import { getRequestUser, SearchAgentHelpers } from '@/helpers';
import { SearchThread, User } from '@/payload-types';
import { searchProducts } from './search-agent-products';
import { summarizeRequirementsFromHistory, validateSearchResults } from './requirements-prompts';

import { ComponentSearchScore } from '@repo/dcide-component-models';

export const TEMPERATURE = 0.2;
export const MAX_ITERATIONS = 4;
const LAST_ITERATION = MAX_ITERATIONS - 1;
export const RESULT_LIMIT = 12;
export const SHOWN_RESULT_LIMIT = 4;

const searchAgentSchema = z.object({
    question: z.string(),
    id: z.string().optional(),
    event: z.string().optional(),
});

const SEARCH_AGENT_SYSTEM_PROMPT = `
    You are an expert matchmaker for energy projects — a friendly and curious power systems specialist who helps people find the right product, service, partner, or idea to move their project forward.
    - suggest relevant products that fit their requirements
    - reference case studies when users ask how to implement a solution
    - Answer directly, avoiding tangents.
    - If the user is asking for a product only provide results from the searchProducts tool.
    - If the user is asking for a specific company only provide results from the searchCompanies tool.
    - If the user is asking for case studies only provide results from the searchCaseStudies tool.

    Your tone and style:
    - Friendly, professional, and practical.
    - Explain in plain language, but back up with technical reasoning when needed.
    - No buzzwords or overcomplicated theory.
    - Use bullet points, tables, or step-by-step instructions when helpful.
    - Never assume too much—ask thoughtful follow-up questions to refine recommendations.
    - Offer optional next steps like sizing assistance, diagram mockups, or comparison of options.

    You also have basic engineering judgment — when someone gives you partial data (e.g. "I want 90kW of solar"), you:
    - Do basic math or logic to estimate related values (e.g. minimum battery size, typical DC/AC ratio, expected load)
    - Use those estimates to improve your recommendations
    - Clearly explain how you arrived at them (e.g. “With 90kW solar, a typical battery size for 4 hours of backup would be ~360kWh.”)

    Guide the user through the conversation:
    - If the user’s request is broad, partial, or ambiguous (e.g., “looking for a converter” or “I want a 30 kW system”), do NOT search immediately. Instead, always ask at least one clarifying question to better understand the user’s requirements (e.g., about voltage, application, location, or other relevant details). Only proceed to search after receiving a clarifying response or if the user insists on searching right away.
    - If the user’s request is highly specific and contains all necessary details for a meaningful search (e.g., “I need a 30 kW, 480V grid-tied solar inverter for commercial use”), then proceed to search immediately without asking for clarification.
    - Do not overwhelm the user with too many questions—focus on the most relevant details to improve the match.
    - If the user is clearly searching for products, companies, or case studies and their request can be fulfilled as-is, respond helpfully and do not ask for more details.
    - Only ask specific clarifying questions if the user's request is ambiguous, contradictory, or clearly insufficient for meaningful results.
    - Respect users who want to browse or search on their own—do not over-engage if not needed.

    Work step-by-step. After each answer from the user, peform self-reflection and assessment and suggest an appropriate next action to take from the list provided below. The list is unstructured, to feel free to reorder the actions as you see fit. You can also add new actions if you think they are relevant:

    * (optional) Ask for clarification
        - Figure out whether the user is looking for a specific product, service, or company, or just browsing for inspiration.
        - You may ask the user for more details about what they are looking for, such as for example location, budget, timeline or specific product requirements.
        - You may ask if they have any specific technologies or brands in mind.
        - Do not ask for clarification if not needed.
    * (optional) Search for companies
        - Check the previous function_call_output for results before using the searchCompanies tool.
    * (optional) Search for products using searchProducts tool
        - The searchProducts tool can search for products in the electric power industry.
        - power, voltage, and current are required to search for compatible products.
        - keep search filters concise to a minimum:
            - example: "converter 800 V to 450 V Bidirectional 40 kW AC/DC"
            - example: "converter 11 kW maximum DC output, isolated, AC power source, 130 V nominal"
            - example: "battery 800 V 40 kW"
            - example: "40V solar panel 500W bifacial"
            - example: "three phase 480vac 20kW converter"
            - example: "25kw 5a 240v converter"
        - search filters should not be a description: "batteries compatible with ..."
        - Only use for new searches, not for show more results.
        - As a follow-up action we can:
            > search for companies (suggest setting the service filter to manufacturer)
            > search for more products
            > validate the results for the user.
        - Pass the :emphasis parameter as follows:
            > If you are searching for products compatible with another product, use the compatibleProduct emphasis.
            > If you are searching for a product with electrical port specifications, use the portMatch emphasis.
            > If you are searching for a battery and the energy capacity is specified, use the energyCapacity emphasis.
            > Otherwise, use the default emphasis.
        - If the user has provided specific requirements, summarize them and pass them as the requirements: parameter.
    * (optional) Use the searchProducts tool to find compatible products using: power, voltage, and current
    * (optional) Show more product results
        - Check the previous function_call_output for results before using the showMoreProductResults tool.
    * (optional) Search for case studies
        - Check the previous function_call_output for results before using the searchCaseStudies tool.
        - Use the searchCaseStudies tool
    * (optional) Summarize the results
        - Provide a summary of the results from the previous searches, including key findings and recommendations.
    * (optional) For questions about a product, that may be found in operating manuals or application notes, use the getProductDocumentation tool:
        - Use searchProducts tool to get product specifications: power, voltage, current
        - Do not use the getProductDocumentation tool to get power, voltage, or current
        - Use the getProductDocumentation tool to retrieve product documentation details using the product: value
    * (important) Use searchCompanies tool, when the user asks to connect with companies
        - Ask for project details if the user has not provided any.
        - As a follow-up action we can:
            > share the user requirements with companies
    * (important) Use shareUserRequirements tool, when the user asks to share their requirements with companies
    * (important) Ask the user to leave their email address so we can help them further.
        - Always do this when:
            - No relevant results were returned,
            - The assistant is unsure how to proceed,
            - The user gives unclear, confused, or contradictory input (e.g., using incorrect technical terms),
            - The conversation is not progressing toward a useful result,
            - The user seems stuck or unsatisfied.
        - Use the getHelpFromSomeone tool if the user provides a valid email.
        - Only use the getHelpFromSomeone tool once per conversation.
        - Reassure the user that someone will follow up with personal support.
        - This email capture step is more important than continuing the conversation when the assistant is unsure.
        - Do **not** keep suggesting more searches if the conversation isn’t moving forward.

    * Avoid external hyperlinks to keep users on the platform. Use the following formats:
        - Link to product pages: [[product:{productId}]]
        - Link to company profiles: [[company:{companyId}]]

    Never answer with "no results" or "I don't know".

    ---

    **Next Steps (Always End With a Prompt):**
    - BEFORE WRITING ANY RESPONSE, CHECK THE FOLLOWING:
        1. Have I called the sendNextSteps tool with ALL required information?
        2. Have I included at least three next steps?
        3. Have I formatted the steps as a numbered list?
    - If ANY of these checks fail, DO NOT PROCEED with writing the response.
    - The sendNextSteps tool MUST be called FIRST, before any text is written.
    - To indicate next steps, use this special syntax between the main answer and the next steps: "=== NEXT STEPS ===" eg.
        * Main answer
        * === NEXT STEPS ===
        * Intro for main steps: "How would you like to proceed?"
        * Next steps list numbered
        * Intro for refine results: "Help us refine your results:"
        * Refine results list hyphens
    - The Next steps list format MUST be a numbered list (e.g., "1. First option", "2. Second option")
    - For each next step, provide:
        * number: The step number (1, 2, 3, etc.)
        * label: A short label (max 30 characters) for the step that will be shown as a tooltip
        * ONLY provide options that do not require any additional information from the user, for example:
            - "1. Continue browsing"
            - "2. Show more results"
            - "3. Compare options"
    - Format the options as a numbered list (e.g., "1. First option", "2. Second option")
    - Only encourage the user to provide more information if their input is unclear, incomplete, or if more detail would meaningfully improve the results.
    - Offer optional deeper dive "refine results" (e.g., “Would you like help sizing your system, comparing options, or getting a cost estimate?”), but respect users who prefer to explore on their own.
    - Example refine results prompts:
        • "Describe your project requirements for tailored recommendations"
        • "Would you like me to help size the system, or provide a cost estimate?"
    - Never end a response without a clear, engaging next step for the user, unless the user indicates they want to browse/search independently.
    - Always suggest at least three next steps. One should be sharing the user's requirements with companies.
    - IMMEDIATELY display company profiles for ALL found companies using [[company:{companyId}]] format
    - Do NOT ask the user if they want to view company profiles

    Always greet the user and introduce yourself at the start of the conversation, explaining what you can do. Be enthusiastic, friendly, and eager to help.

    Response format:
    * Always always show products using: [[product:{productId}]]
    * Always always show companies using: [[company:{companyId}]]
    * Always always show case study using: [[case_study:{caseStudyId}]]
    * Never link directly to external websites or social media. If a company has a profile on this platform, always link to the company profile ([[company:{companyId}]]) instead.
    * If users request external links, direct them to the company profile, where such links may be available.
    * Exclude images
    {{booth-instructions}}

    Self-Reflection and Assessment:
    - For each set of results you generate, always provide a brief summary of the results.
    - Explicitly assess how well the results match the user's stated requirements, highlighting any gaps, uncertainties, or especially relevant matches.
    - Reflect on the overall quality and relevance of the results. If results are weak or ambiguous, acknowledge this and suggest ways to improve them (e.g., by requesting more details or clarifications or searching again with different search terms).
    - Always suggest an appropriate next action based on your assessment, guiding the user toward a more successful outcome.
`;

const userLoggedInPrompt = (email?: string) => (email ? `The user is logged in with the email address ${email}.` : '');

interface CategorizedFunctionTool extends OpenAI.Responses.FunctionTool {
    category: 'search' | 'tool' | 'sendNextSteps';
}

const tools: CategorizedFunctionTool[] = [
    {
        type: 'function',
        category: 'sendNextSteps',
        name: 'sendNextSteps',
        description: 'Send detailed information about next steps to the frontend',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                steps: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            number: {
                                type: 'number',
                                description: 'The step number (1, 2, 3, etc.)',
                            },
                            label: {
                                type: 'string',
                                description: 'A short label for the step that will be shown as a tooltip',
                            },
                        },
                        required: ['number', 'label'],
                        additionalProperties: false,
                    },
                },
                suggestions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            label: {
                                type: 'string',
                                description: 'A short label for the suggestion to refine results',
                            },
                        },
                        required: ['label'],
                        additionalProperties: false,
                    },
                },
            },
            required: ['steps', 'suggestions'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'search',
        name: 'showMoreProductResults',
        description: 'show more product results',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                previousProductFilters: {
                    type: 'string',
                    description:
                        'Previous filters to apply to the search (default: ""). Only fill this in based on previous function_call_output.',
                },
                page: {
                    type: 'number',
                    description: 'Page number for pagination (default: 0)',
                },
                emphasis: {
                    type: 'string',
                    enum: ['default', 'portMatch', 'energyCapacity', 'compatibleProduct'],
                    description: 'Set an emphasis for the search to order search results',
                },
            },
            required: ['previousProductFilters', 'page', 'emphasis'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'search',
        name: 'searchProducts',
        description: 'Call a search engine to find products',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                searchFilters: {
                    type: 'string',
                    description: 'filters used to filter the product catalog',
                },
                previousProductFilters: {
                    type: 'string',
                    description:
                        'Previous filters to apply to the search (default: ""). Only fill this in based on previous function_call_output.',
                },
                page: {
                    type: 'number',
                    description: 'Page number for pagination (default: 0)',
                },
                requirements: {
                    type: 'string',
                    description: 'project goals or requirements the user has provided',
                },
                emphasis: {
                    type: 'string',
                    enum: ['default', 'portMatch', 'energyCapacity', 'compatibleProduct'],
                    description: 'Set an emphasis for the search to order search results',
                },
            },
            required: ['searchFilters', 'previousProductFilters', 'page', 'requirements', 'emphasis'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'search',
        name: 'searchCompanies',
        description: 'Call a search engine to find company profiles',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                keywords: {
                    type: 'string',
                    description: 'Keywords to search for relevant company profiles',
                },
                page: {
                    type: 'number',
                    description: 'Page number for pagination (default: 0)',
                },
            },
            required: ['keywords', 'page'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'search',
        name: 'searchCaseStudies',
        description: 'Call a search engine to find case studies',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                keywords: {
                    type: 'string',
                    description: 'Keywords to search for relevant case studies',
                },
            },
            required: ['keywords'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'tool',
        name: 'getHelpFromSomeone',
        description: 'Capture the user email address',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                email: {
                    type: 'string',
                    description: 'The user email address',
                },
            },
            required: ['email'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'search',
        name: 'getProductDocumentation',
        description: 'used to lookup product operating manuals or application notes',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                productName: {
                    type: 'string',
                    description: 'productName',
                },
                productId: {
                    type: 'string',
                    description:
                        'productId ( [[product:{productId}]] ) to search for documentation information. The productId is the id of the product in the database.',
                },
                question: {
                    type: 'string',
                    description: 'question to search for product documentation information',
                },
            },
            required: ['productName', 'productId', 'question'],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        category: 'tool',
        name: 'shareUserRequirements',
        description:
            'Share the user requirements with companies they match with. Always print the requirements summary to the user.',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                email: {
                    type: 'string',
                    description: 'The user email address',
                },
            },
            required: ['email'],
            additionalProperties: false,
        },
    },
];

const parsePreviousFilters = (previousFiltersAsString: string | undefined) => {
    if (!previousFiltersAsString) return undefined;
    try {
        return JSON.parse(previousFiltersAsString);
    } catch (e) {
        console.warn('Error parsing previous filters:', e);
        return undefined;
    }
};

const ProductSearchEmphasisWeights: Record<string, Partial<Record<ComponentSearchScore, number>>> = {
    default: {},
    portMatch: {
        [ComponentSearchScore.PORT_MATCH_SCORE]: 10,
    },
    energyCapacity: {
        [ComponentSearchScore.ENERGY_CAPACITY_SCORE]: 10,
    },
    compatibleProduct: {
        [ComponentSearchScore.COMPATIBLE_PRODUCT]: 10,
    },
};

const handleToolCall = async (
    toolCall: OpenAI.Responses.ResponseFunctionToolCall,
    options: {
        user: User | null;
        question: string;
        searchThreadId?: string;
        tradeshowEvent?: string;
        payload: Payload;
        onUpdate?: (message: string, details?: string) => void;
        writer?: WritableStreamDefaultWriter;
    },
) => {
    const { name, arguments: args } = toolCall;
    const { question, searchThreadId, tradeshowEvent, payload, onUpdate, writer } = options;

    let output = '';

    if (name === 'getHelpFromSomeone') {
        const { email } = JSON.parse(args);

        SlackService.send(
            [
                `🤖 RE+ Search Agent email capture: ${email}`,
                `View thread: ${config.urls.backend}/admin/collections/searchThreads/${searchThreadId}`,
            ].join('\n'),
        );

        if (searchThreadId && email) {
            await payload.update({
                collection: 'searchThreads',
                id: searchThreadId,
                data: {
                    email: email,
                },
            });
        }
    } else if (name === 'showMoreProductResults') {
        const { previousProductFilters: previousFiltersAsString, page: page_, emphasis } = JSON.parse(args);

        console.log(
            '[showMoreProductResults] Searching for products with args:',
            JSON.stringify({ previousProductFilters: previousFiltersAsString, page: page_, emphasis }, null, 2),
        );

        onUpdate?.(`Searching for products`, `Page: ${page_} Emphasis: "${emphasis}"`);

        const previousFilters = parsePreviousFilters(previousFiltersAsString);
        const filters = previousFilters ?? (await SearchService.getComponentFilters(previousFiltersAsString, payload));
        const page: number = page_ || 0;

        console.log(
            `[showMoreProductResults] Getting page ${page} results, using filters:`,
            JSON.stringify(filters, null, 2),
        );

        const searchResults = await SearchService.getComponents(
            payload,
            { ...filters, event: tradeshowEvent },
            {
                limit: RESULT_LIMIT,
                page,
                customWeights: ProductSearchEmphasisWeights[emphasis],
                project: {
                    id: { $toString: '$_id' },
                    name: 1,
                    description: 1,
                    productSeries: 1,
                    productIdentifier: 1,
                    type: 1,
                    manufacturer: 1,
                    manufacturerDetails: 1,
                    compliance: 1,
                    embeddingText: 1,
                    specificationsSummary: 1,
                    electrical: 1,
                    communication: 1,
                    environmental: 1,
                    performance: 1,
                    mechanical: 1,
                },
            },
        );
        searchResults.docs = searchResults.docs.map((product) => ({
            productId: product._id.toString(),
            name: product.name,
            type: product.type,
            description: product.description,
            productSeries: product.productSeries,
            productIdentifier: product.productIdentifier,
            manufacturer: product.manufacturer,
            manufacturerDetails: product.manufacturerDetails,
            embeddingText: product.embeddingText,
            specificationsSummary: product.specificationsSummary,
        }));

        const saveProductsAsComponentIds: string[] =
            searchResults?.docs?.map((product: any) => product.productId) ?? [];
        AIService.upsertExhibitorMatchResults(
            searchThreadId,
            {
                productSearchResults: saveProductsAsComponentIds,
            },
            tradeshowEvent,
        );

        const noResults = searchResults.docs.length === 0;

        output = JSON.stringify(
            {
                searchResults,
                page: noResults ? undefined : page,
                previousProductFilters: noResults ? undefined : JSON.stringify(filters),
            },
            null,
            2,
        );
    } else if (name === 'searchProducts') {
        const { searchFilters, requirements } = JSON.parse(args);
        const { searchResults, previousProductFilters } = await searchProducts(
            searchFilters,
            requirements,
            question,
            searchThreadId,
            tradeshowEvent,
            onUpdate,
            writer,
        );

        output = JSON.stringify(
            {
                searchResults,
                page: undefined,
                previousProductFilters,
            },
            null,
            2,
        );
    } else if (name === 'searchCompanies') {
        const { keywords, page: page_ } = JSON.parse(args);
        console.log('Searching for companies with args:', JSON.stringify({ keywords }, null, 2));

        console.log(`Searching for companies with keywords: ${keywords}...`);
        onUpdate?.(`Searching for companies`, `Keywords: "${keywords}" Page: ${page_}`);

        const filters = await SearchService.getProfileFilters(keywords, payload);
        const page: number = page_ || 0;
        console.log(`[searchCompanies] Getting page ${page} results, using filters:`, JSON.stringify(filters, null, 2));

        const searchResults = await SearchService.getProfiles(
            payload,
            { ...filters, event: tradeshowEvent },
            {
                limit: RESULT_LIMIT,
                page,
            },
        );

        searchResults.docs = searchResults.docs.map((company) => ({
            companyId: company._id.toString(),
            name: company.name,
            type: company.type,
            highlight: company.highlight,
            highlights: company.highlights,
            promos: company.promos,
            systemSize: company.systemSize,
            services: company.services,
            otherServices: company.otherServices,
            about: company.about,
            description: company.description,
            locations: company.locations,
            applicationTags: company.applicationTags,
            projectBudget: company.projectBudget,
            serviceTags: company.serviceTags,
            embeddingText: company.embeddingText,
            booth: company.booth,
        }));

        const saveCompaniesAsManufacturerIds: string[] =
            searchResults?.docs?.map((company: any) => company.companyId) ?? [];
        AIService.upsertExhibitorMatchResults(
            searchThreadId,
            { companySearchResults: saveCompaniesAsManufacturerIds },
            tradeshowEvent,
        );

        const noResults = searchResults.docs.length === 0;

        output = JSON.stringify(
            {
                searchResults,
                page: noResults ? undefined : page,
                previousCompanyFilters: noResults ? undefined : JSON.stringify(filters),
            },
            null,
            2,
        );
    } else if (name === 'getProductDocumentation') {
        const { productId, question } = JSON.parse(args);
        console.log(`Searching for product documentation with productId: ${productId} and question: ${question}...`);
        onUpdate?.(`Searching the product documentation`, `Question: "${question}"`);

        const jwt = undefined;
        const searchResults = await AIService.qna(productId, question, jwt);

        output = JSON.stringify(searchResults, null, 2);
    } else if (name === 'shareUserRequirements') {
        const { email } = JSON.parse(args);

        const searchThread = await SearchAgentHelpers.getExistingSearchThread(payload, searchThreadId);
        if (!email && !searchThread?.email) {
            output = 'Please provide an email address to get matched with companies';
        } else {
            const newEmail = searchThread?.email ? searchThread.email : email;
            if (newEmail && searchThreadId) {
                await payload.update({
                    collection: 'searchThreads',
                    id: searchThreadId,
                    data: {
                        email: newEmail,
                    },
                });
            }
        }

        if (!output) {
            const searchResults = await summarizeRequirementsFromHistory(
                searchThreadId,
                (searchThread?.thread as any)?.input,
                onUpdate,
                tradeshowEvent,
            );
            output = searchResults;
        }
    } else if (name === 'searchCaseStudies') {
        const { keywords } = JSON.parse(args);

        onUpdate?.(`Searching for case studies`, `Keywords: "${keywords}"`);

        const searchResults = await SearchService.getCaseStudies(payload, keywords, tradeshowEvent, {
            limit: RESULT_LIMIT,
        });
        const saveCaseStudiesAsArticleIds: string[] =
            searchResults?.docs.map((caseStudy: any) => caseStudy.collectionRecordId.toString()) ?? [];
        AIService.upsertExhibitorMatchResults(
            searchThreadId,
            { caseStudiesResults: saveCaseStudiesAsArticleIds },
            tradeshowEvent,
        );

        console.log(`Tool call: ${name}, arguments: ${args}`, searchResults);

        output = JSON.stringify(searchResults, null, 2);
    } else if (name === 'validateSearchResults') {
        const { keywords, searchResults } = JSON.parse(args);
        console.log(`Analyzing search results with keywords: ${keywords}...`);
        onUpdate?.(`Analyzing the search results`, `Keywords: "${keywords}"`);

        const searchResultsValidated = await validateSearchResults(keywords, searchResults);
        console.log(`Tool call: ${name}, arguments: ${args}`, searchResultsValidated);
        output = searchResultsValidated;
    } else if (name === 'sendNextSteps') {
        const { steps } = JSON.parse(args);
        output = JSON.stringify({ steps });

        console.log('Sending next steps to frontend:', JSON.stringify(steps, null, 2));
    }

    return {
        toolCall,
        output,
    };
};

const getExistingSearchThreadInput = (searchThread: SearchThread | null) => {
    if (!searchThread) {
        return null;
    }

    const threadInput = (searchThread?.thread as any)?.input;
    if (!threadInput || !Array.isArray(threadInput) || threadInput.length === 0) {
        return null;
    }

    return threadInput;
};

const generateInput = async (
    payload: Payload,
    user: User | null,
    request: PayloadRequest,
    threadId: string | undefined,
    question: string,
    tradeshowEvent?: string,
) => {
    const searchThread = await SearchAgentHelpers.getExistingSearchThread(request.payload, threadId);
    const existingSearchThreadInput = getExistingSearchThreadInput(searchThread);

    const input: OpenAI.Responses.ResponseInput = [];

    if (existingSearchThreadInput) {
        input.push(...existingSearchThreadInput);
    }

    if (user?.email) {
        input.push({
            role: 'system',
            content: `${userLoggedInPrompt(user?.email)}`,
        });
    }

    if (tradeshowEvent) {
        await SearchAgentHelpers.addTradeShowEvent(payload, tradeshowEvent, input);
    }

    input.push({ role: 'user', content: question });

    return input;
};

const removeOldFunctionCallData = (input: OpenAI.Responses.ResponseInput, historyLength = 3) => {
    const clone = JSON.parse(JSON.stringify(input)) as OpenAI.Responses.ResponseInput;

    const functionCalls: {
        [k: string]: any[];
    } = {};

    clone.forEach((item) => {
        if (item.type === 'function_call') {
            const type = item.name;

            functionCalls[type] = functionCalls[type] || [];
            functionCalls[type].push(item.call_id);
        }
    });

    Object.values(functionCalls).forEach((ids) => {
        if (ids.length > historyLength) {
            const removable = ids.slice(0, ids.length - historyLength);

            clone.forEach((item, index) => {
                if (item.type === 'function_call_output' && removable.includes(item.call_id)) {
                    // @ts-ignore
                    clone[index].output = '**stale data to be ignored**';
                }
            });
        }
    });

    return clone;
};

const getApiKey = () => {
    const apiKey = config.openai.apiKey;

    if (!apiKey) {
        throw new Error('API key not configured.');
    }

    return apiKey;
};

export const searchAgent2: Endpoint = {
    path: '/search-agent2',
    method: 'post',
    handler: async (request) => {
        try {
            const apiKey = getApiKey();

            const openai = new OpenAI({ apiKey });

            const body = await request.json!();

            const { id: searchThreadId, question, event: tradeshowEvent } = searchAgentSchema.parse(body);

            const user = await getRequestUser(request);

            const input: OpenAI.Responses.ResponseInput = await generateInput(
                request.payload,
                user,
                request,
                searchThreadId,
                question,
                tradeshowEvent,
            );

            const stream = new TransformStream();
            const writer = stream.writable.getWriter();
            const encoder = new TextEncoder();
            let hasPreviousMessage = false;

            const { signal } = new AbortController();

            // Send keep-alive messages every 30 seconds to maintain connection
            const keepAlive = setInterval(async () => {
                if (!signal.aborted) {
                    try {
                        await writer.write(encoder.encode(`event: ping\ndata: ${JSON.stringify('keep-alive')}\n\n`));
                    } catch (error) {
                        console.error('Error writing keep-alive message:', error);
                        clearInterval(keepAlive);
                    }
                }
            }, 30000);

            // Clean up intervals and close writer when connection is aborted
            signal.addEventListener('abort', async () => {
                clearInterval(keepAlive);
                await writer.close();
            });

            const streamUpdateToUser = async (message: string, details?: string) => {
                if (signal.aborted) return;

                try {
                    let updatePayload = `agent-update:${message}`;
                    if (details) {
                        updatePayload += `:::details:${details}`;
                    }

                    await writer.write(encoder.encode(`event: update\ndata: ${JSON.stringify(updatePayload)}\n\n`));

                    if (hasPreviousMessage) {
                        await writer.write(encoder.encode(`data: ${JSON.stringify('\n\n')}\n\n`));
                    }

                    hasPreviousMessage = false;
                } catch (error) {
                    console.error('Error writing update message:', error);
                }
            };

            const sendNextStepsEvent = async (
                steps: Array<{
                    number: number;
                    label: string;
                }>,
                suggestions: Array<{
                    label: string;
                }>,
            ) => {
                if (signal.aborted) return;

                try {
                    await writer.write(
                        encoder.encode(
                            `event: next-steps\ndata: ${JSON.stringify(`next-steps:${JSON.stringify({ steps, suggestions })}`)}\n\n`,
                        ),
                    );
                } catch (error) {
                    console.error('Error writing next-steps message:', error);
                }
            };

            (async () => {
                let toolCalls: OpenAI.Responses.ResponseFunctionToolCall[] = [];
                let reasoningCalls: OpenAI.Responses.ResponseReasoningItem[] = [];

                for (let iteration = 0; iteration <= MAX_ITERATIONS; iteration++) {
                    try {
                        if (signal.aborted) break;

                        console.log('ITERATION', iteration);
                        const toolsToCall =
                            iteration === LAST_ITERATION
                                ? tools.filter((tool) => tool.category === 'sendNextSteps')
                                : tools;

                        if (reasoningCalls.length > 0) {
                            reasoningCalls.forEach((reasoningCall) => {
                                input.push(reasoningCall);
                            });
                        }
                        if (toolCalls.length > 0) {
                            const toolCallResults = await Promise.all(
                                toolCalls.map((toolCall) =>
                                    handleToolCall(toolCall, {
                                        user,
                                        question,
                                        searchThreadId,
                                        tradeshowEvent,
                                        payload: request.payload,
                                        onUpdate: streamUpdateToUser,
                                        writer,
                                    }),
                                ),
                            );

                            toolCallResults.filter(Boolean).forEach(({ output, toolCall }) => {
                                input.push(toolCall);
                                input.push({
                                    type: 'function_call_output',
                                    call_id: toolCall.call_id,
                                    output: output ?? '',
                                });
                            });

                            toolCalls = []; // clear for this iteration
                        }
                        reasoningCalls = []; // clear reasoning calls for this iteration

                        const systemPrompt = SearchAgentHelpers.updateSystemPromptWithInstructions(
                            SEARCH_AGENT_SYSTEM_PROMPT,
                            tradeshowEvent,
                        );
                        const aiStream = await openai.responses.create({
                            model: config.aiModels.searchAgent,
                            instructions: systemPrompt,
                            input: removeOldFunctionCallData(input),
                            stream: true,
                            tools: toolsToCall,
                            tool_choice: 'auto',
                            parallel_tool_calls: false,
                            reasoning: {
                                effort: 'low',
                            },
                        });

                        signal.addEventListener('abort', aiStream.controller?.abort);

                        for await (const event of aiStream) {
                            if (signal.aborted) break;

                            if (event.type === 'response.output_text.delta') {
                                await writer.write(encoder.encode(`data: ${JSON.stringify(event.delta)}\n\n`));
                                hasPreviousMessage = true;
                            } else if (event.type === 'response.output_text.done') {
                                input.push({
                                    id: event.item_id,
                                    role: 'assistant',
                                    content: event.text,
                                });

                                if (searchThreadId) {
                                    await request.payload.update({
                                        collection: 'searchThreads',
                                        id: searchThreadId,
                                        data: {
                                            thread: {
                                                input,
                                            },
                                            ...(user ? { email: user.email } : {}),
                                        },
                                    });
                                }
                            } else if (event.type === 'response.output_item.added') {
                                if (event.item.type === 'function_call') {
                                    toolCalls[event.output_index] = event.item;
                                }
                                if (event.item.type === 'reasoning') {
                                    reasoningCalls.push(event.item);
                                }
                            } else if (event.type === 'response.function_call_arguments.delta') {
                                const index = event.output_index;

                                if (toolCalls[index]) {
                                    toolCalls[index].arguments += event.delta;
                                }
                            } else if (event.type === 'response.function_call_arguments.done') {
                                const index = event.output_index;

                                if (toolCalls[index]) {
                                    toolCalls[index].status = 'completed';

                                    // If this was a sendNextSteps call, send the event to the frontend
                                    if (toolCalls[index].name === 'sendNextSteps') {
                                        const args = JSON.parse(toolCalls[index].arguments);
                                        await sendNextStepsEvent(args.steps, args.suggestions);
                                    }
                                }
                            }
                        }

                        // If no tool calls, break the loop
                        if (toolCalls.length === 0) {
                            break;
                        }
                    } catch (error) {
                        console.log('ERROR', error);

                        if (!signal.aborted) {
                            await writer.write(encoder.encode(`event: error\ndata: {"message": "Stream failed"}\n\n`));
                        }
                        break;
                    }
                }

                clearInterval(keepAlive);
                await writer.close();
            })().then();

            return new Response(stream.readable, {
                status: 200,
                headers: {
                    'Content-Type': 'text/event-stream',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'X-Accel-Buffering': 'no',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                },
            });
        } catch (error) {
            console.error('Error in /search-agent endpoint: ', error);
            return new Response(JSON.stringify({ error: 'Failed to process request.' }), {
                status: 500,
                headers: { 'Content-Type': 'application/json' },
            });
        }
    },
};
