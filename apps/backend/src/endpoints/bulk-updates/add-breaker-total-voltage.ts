import { Poles } from '@repo/dcide-component-models';
import { Payload } from 'payload';
import { isNumber } from 'radash';

export const addBreakerTotalVoltage = async (payload: Payload) => {
    const ports = await payload.db.collections['components'].aggregate([
        {
            $match: {
                type: 'breaker',
            },
        },
        {
            $addFields: {
                id: {
                    $toString: '$_id',
                },
            },
        },
        {
            $lookup: {
                from: 'componentports',
                localField: 'id',
                foreignField: 'component',
                as: 'portIds',
            },
        },
        {
            $unwind: {
                path: '$portIds',
            },
        },
        {
            $replaceRoot: {
                newRoot: '$portIds',
            },
        },
    ]);

    let updateCount = 0;

    await Promise.all(
        ports.map((port) => {
            const update: any = {};

            const acVoltagePerPole = port.data.AC.voltagePerPole.value;
            const dcVoltagePerPole = port.data.DC.voltagePerPole.value;

            if (isNumber(acVoltagePerPole)) {
                update['data.AC.totalVoltage'] = {
                    value: acVoltagePerPole * Poles.numericValueMap[port.data.AC.poles as Poles],
                    unit: 'V',
                };
            }

            if (isNumber(dcVoltagePerPole)) {
                update['data.DC.totalVoltage'] = {
                    value: dcVoltagePerPole * Poles.numericValueMap[port.data.DC.poles as Poles],
                    unit: 'V',
                };
            }

            return payload.db.collections['componentPorts']
                .updateOne({ _id: port._id }, { $set: update })
                .then(() => (updateCount += 1));
        }),
    );

    console.log(`Updated ${updateCount} records`);
};
