import { Payload } from 'payload';
import { getCollection, getId } from '@/helpers';

interface UpdatedManufacturerLog {
    id: string;
    name: string;
    originalUsers: string[];
    newUsers: string[];
}

export const updateManufacturerUsers = async (payload: Payload): Promise<UpdatedManufacturerLog[]> => {
    const manufacturers = await payload.find({
        collection: 'manufacturers',
        depth: 0,
        pagination: false,
        select: { name: true, team: true, users: true },
    });

    const updatedManufacturers: UpdatedManufacturerLog[] = [];

    await Promise.all(
        manufacturers.docs.map(async (manufacturer) => {
            const team =
                typeof manufacturer.team === 'string'
                    ? await payload.findByID({
                          collection: 'teams',
                          id: manufacturer.team,
                          depth: 0,
                      })
                    : manufacturer.team;

            if (!manufacturer.users || !team?.users) return;

            const manufacturerUserIds = manufacturer.users.map((user) => getId(user));
            const teamUserIds = team.users.map((teamUser) => getId(teamUser.user));
            const usersToRemove = manufacturerUserIds.filter((userId) => !teamUserIds.includes(userId));

            if (usersToRemove.length > 0) {
                let newUsers = manufacturer.users
                    .filter((user) => !usersToRemove.includes(getId(user)))
                    .map((user) => getId(user));

                if (newUsers.length === 0) {
                    newUsers = teamUserIds;
                }

                await payload.update({
                    collection: 'manufacturers',
                    id: manufacturer.id,
                    data: {
                        users: newUsers,
                    },
                    depth: 0,
                });

                updatedManufacturers.push({
                    id: manufacturer.id,
                    name: manufacturer.name,
                    originalUsers: manufacturerUserIds,
                    newUsers: newUsers.map((user) => getId(user)),
                });
            }
        }),
    );

    console.log('Updated manufacturers', updatedManufacturers);

    return updatedManufacturers;
};
