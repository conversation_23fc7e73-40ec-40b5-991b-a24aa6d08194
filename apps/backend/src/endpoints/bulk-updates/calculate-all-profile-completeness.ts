import { PayloadRequest } from 'payload';

export const calculateAllProfileCompleteness = async (req: PayloadRequest) => {
    const counts = { updatedCount: 0, errorCount: 0 };

    const manufacturers = await req.payload.find({
        collection: 'manufacturers',
        pagination: false,
        depth: 0,
    });

    for (const manufacturer of manufacturers.docs) {
        try {
            await req.payload.update({
                collection: 'manufacturers',
                id: manufacturer.id,
                data: {
                    completeness: 0, // trigger calculateCompleteness hook
                },
                depth: 0,
                req,
            });

            counts.updatedCount += 1;
        } catch (error) {
            counts.errorCount += 1;
            console.error(error);
        }
    }

    console.log(counts);
};
