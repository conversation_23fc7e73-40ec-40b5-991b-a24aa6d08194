import type { Endpoint, Payload } from 'payload';

import { User } from '@/payload-types';
import { handleEndpointError } from '../../helpers';
import { BadRequestError, ForbiddenError } from '../../responses/errors';
import { FileSchema, FileType, UploadType } from './types';
import { userHasReviewOrderPermission, userIsInOrderTeam } from '../../access/orders';

const addFile: Endpoint = {
    path: '/add-file',
    method: 'post',
    handler: async (request) => {
        try {
            if (!request.user) {
                throw new ForbiddenError();
            }

            const { associateWith, associatedId, file, url, type, diagramId } = FileSchema.parse(await request.json!());

            const args = { id: associatedId, user: request.user, associateWith, file, url, type, diagramId };

            switch (args.associateWith) {
                case 'project':
                case 'design':
                    return uploadCollectionRootFile(request.payload, args);

                case 'order':
                    return uploadOrderFile(request.payload, args);

                default:
                    throw new BadRequestError(`Invalid upload type: ${args.associateWith}`);
            }
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

type Uploader = (
    payload: Payload,
    args: {
        associateWith: UploadType;
        id: string;
        type?: FileType;
        user: User;
        file?: string;
        url?: string;
        diagramId?: string;
    },
) => Promise<Response>;

const uploadCollectionRootFile: Uploader = async (payload, { associateWith, id, type, file, url }) => {
    const collection = associateWith === 'design' ? 'projectDesigns' : 'projects';

    const item = await payload.findByID({ collection, id, depth: 0 });

    const updatedItem = await payload.update({
        collection,
        id,
        data: {
            files: [...(item.files ?? []), { file, url, type }],
        },
    });

    return Response.json(updatedItem);
};

const uploadOrderFile: Uploader = async (payload, { id, user, file, url, type }) => {
    const order = await payload.findByID({
        collection: 'orders',
        id,
        depth: 0,
    });

    const userHasReviewPermission = await userHasReviewOrderPermission(user, order);

    if (!(userIsInOrderTeam({ user, order }) || userHasReviewPermission)) {
        throw new ForbiddenError();
    }

    if (order.status === 'archived' || order.status === 'deleted') {
        throw new BadRequestError(`Cannot add files to ${order.status} orders.`);
    }

    const updatedOrder = await payload.update({
        collection: 'orders',
        id: order.id,
        data: {
            files: [...(order.files ?? []), { file, url, type }],
        },
        depth: 0,
    });

    return Response.json(updatedOrder);
};

export { addFile };
