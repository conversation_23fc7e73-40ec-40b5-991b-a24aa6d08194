import { generateFeaturedProducts } from '@/search/component/generate-featured-products';
import { generateCronEndpoint } from './generate-cron-endpoint';

const updateFeaturedProducts = generateCronEndpoint({
    subPath: '/update-featured-products',
    method: 'get',
    handler: async (request) => {
        try {
            const productIds = await generateFeaturedProducts(request.payload);

            await request.payload.updateGlobal({
                slug: 'general',
                data: { featuredProducts: productIds },
            });

            return Response.json({ success: true, productIds });
        } catch (error) {
            console.error('Error updating discover products', error);
            return Response.json({ error }, { status: 500 });
        }
    },
});

export { updateFeaturedProducts };
