import { Endpoint } from 'payload';
import { ForbiddenError } from '@/responses/errors';

type GenerateCronEndpointConfig = {
    subPath: string;
} & Pick<Endpoint, 'handler' | 'method'>;

const ALLOWED_TOKENS = [`Bearer ${process.env.CRON_SECRET}`, process.env.DEVOPS_TOKEN];

export const generateCronEndpoint = ({ subPath, handler, ...config }: GenerateCronEndpointConfig): Endpoint => ({
    path: `/crons${subPath}`,
    handler: async (request) => {
        const token = request.headers.get('authorization');

        if (!token || !ALLOWED_TOKENS.includes(token)) {
            return new ForbiddenError();
        }

        return await handler(request);
    },
    ...config,
});
