import { CompanySubscription, DesignerSubscription } from '@repo/dcide-component-models';
import { z } from 'zod';
import { getValue, getArrayValue } from '@repo/dcide-component-models';

const configSchema =
    typeof window === 'undefined' && process.env.NODE_ENV !== 'production' && process.env.CI_MIGRATING !== 'true'
        ? z.object({
              environment: z.string().default('development'),
              environmentLockAccess: z.string().optional(),
              port: z.string().default('3000'),
              urls: z.object({
                  frontend: z.string(),
                  backend: z.string(),
                  ai: z.string(),
              }),
              database: z.object({
                  url: z.string(),
                  autoCreate: z.boolean(),
                  autoIndex: z.boolean(),
              }),
              payload: z.object({
                  secret: z.string(),
                  configPath: z.string(),
              }),
              email: z.object({
                  sendgridApiKey: z.string().optional(),
                  from: z.object({
                      email: z.string(),
                      name: z.string(),
                  }),
                  bcc: z.string().array().optional(),
                  useLocalEmail: z.boolean().optional(),
              }),
              digitalOcean: z.object({
                  spaces: z.object({
                      url: z.string(),
                      endpoint: z.string(),
                      accessKey: z.string(),
                      secretKey: z.string(),
                      bucket: z.string(),
                      subfolder: z.string(),
                  }),
              }),
              slack: z.object({
                  newUserWebhook: z.string().optional(),
              }),
              grafana: z.object({
                  endpoint: z.string().optional(),
                  token: z.string().optional(),
              }),
              sentry: z.object({
                  dsn: z.string().optional(),
              }),
              stripe: z.object({
                  secretKey: z.string(),
                  [DesignerSubscription.FREE]: z.object({
                      id: z.string(),
                      monthly: z.string().array(),
                      yearly: z.string().array(),
                  }),
                  [DesignerSubscription.PLUS]: z.object({
                      id: z.string(),
                      monthly: z.string().array(),
                      yearly: z.string().array(),
                  }),
                  [DesignerSubscription.PRO]: z.object({
                      id: z.string(),
                      monthly: z.string().array(),
                      yearly: z.string().array(),
                  }),
                  [CompanySubscription.FREE]: z.object({
                      id: z.string(),
                      monthly: z.string().array(),
                      yearly: z.string().array(),
                  }),
                  [CompanySubscription.PREMIUM]: z.object({
                      id: z.string(),
                      monthly: z.string().array(),
                      yearly: z.string().array(),
                      coupon: z
                          .object({
                              monthly: z.string().optional(),
                              yearly: z.string().optional(),
                          })
                          .optional(),
                  }),
                  webhookSecret: z.string(),
              }),
              ably: z.object({
                  apiKey: z.string(),
              }),
              proxycurl: z.object({
                  apiKey: z.string(),
              }),
              zendesk: z.object({
                  url: z.string(),
              }),
              hubspot: z.object({
                  accessToken: z.string().default(''),
              }),
              simulation: z.object({
                  endpoint: z.string(),
              }),
              wireSizing: z.object({
                  endpoint: z.string(),
              }),
              dcideAiService: z.object({
                  apiKey: z.string(),
              }),
              openai: z.object({
                  apiKey: z.string().optional(),
              }),
              gemini: z.object({
                  projectId: z.string().optional(),
                  location: z.string().optional(),
                  apiKey: z.string().optional(),
                  credentialsBase64: z.string().optional(),
                  model: z.string().optional(),
              }),
              aiModels: z.object({
                  default: z.string(),
                  agent: z.string(),
                  searchAgent: z.string(),
                  productAgent: z.string(),
                  diagram: z.string(),
                  compatiblity: z.string(),
                  scoreResults: z.string().optional(),
              }),
              search: z.object({
                  vectorSearch: z.object({
                      annLimit: z.number(),
                      annCandidates: z.number(),
                      ennLimit: z.number(),
                      enabled: z.boolean(),
                  }),
                  textSearch: z.object({
                      enabled: z.boolean(),
                      nameBoost: z.number(),
                      productIdentifierBoost: z.number(),
                      productSeriesBoost: z.number(),
                      embeddingTextBoost: z.number(),
                      locationBoost: z.number(),
                      locationPivot: z.number(),
                  }),
                  aggregateScoreThreshold: z.number(),
              }),
              premiumManufacturerCutoffDate: z.date(),
              rePlusSalesEmail: z.string().email().optional(),
          })
        : z.any(); // we use z.any for when the config gets loaded in the browser

export type Config = z.infer<typeof configSchema>;
export type ClientConfig = {
    urls: {
        frontend: string;
        backend: string;
    };
};

export const clientConfig: ClientConfig = {
    urls: {
        get frontend(): string {
            const isStaging = window?.location.origin.includes('staging') ?? false;
            if (isStaging) return 'https://staging.dcide.app';

            const isProduction = window?.location.origin.includes('api.dcide.app') ?? false;
            if (isProduction) return 'https://www.dcide.app';

            const isLocal = window?.location.origin.includes('localhost') ?? false;
            if (isLocal) return 'http://localhost:3001';

            throw new Error('Unknown environment');
        },
        get backend(): string {
            return window?.location.origin ?? '';
        },
    },
};

export const config: Config = configSchema.parse({
    environment: process.env.ENVIRONMENT,
    environmentLockAccess: process.env.ENVIRONMENT_LOCK_ACCESS,
    port: process.env.PORT,
    urls: {
        frontend: process.env.NEXT_PUBLIC_FRONTEND_URL,
        backend: process.env.NEXT_PUBLIC_SERVER_URL,
        ai: process.env.NEXT_PUBLIC_AI_URL,
    },
    database: {
        url: process.env.MONGODB_URI,
        autoCreate: getValue('MONGODB_AUTO_CREATE', false),
        autoIndex: getValue('MONGODB_AUTO_INDEX', false),
    },
    payload: {
        secret: process.env.PAYLOAD_SECRET,
        configPath: process.env.PAYLOAD_CONFIG_PATH,
    },
    email: {
        sendgridApiKey: process.env.SENDGRID_API_KEY,
        from: {
            email: process.env.EMAIL_FROM_EMAIL,
            name: process.env.EMAIL_FROM_NAME,
        },
        useLocalEmail: getValue('EMAIL_FROM_EMAIL', false),
        bcc: getArrayValue('EMAIL_BCC'),
    },
    digitalOcean: {
        spaces: {
            url: process.env.DO_SPACES_URL,
            endpoint: process.env.DO_SPACES_ENDPOINT,
            accessKey: process.env.DO_SPACES_ACCESS_KEY,
            secretKey: process.env.DO_SPACES_SECRET_KEY,
            bucket: process.env.DO_SPACES_BUCKET,
            subfolder: process.env.DO_SPACES_SUBFOLDER,
        },
    },
    slack: {
        newUserWebhook: process.env.SLACK_WEBHOOK_NEW_USER_URL,
        oemWebhook: process.env.SLACK_WEBHOOK_OEM_URL,
    },
    grafana: {
        endpoint: process.env.GRAFANA_ENDPOINT,
        token: process.env.GRAFANA_TOKEN,
    },
    sentry: {
        dsn: process.env.SENTRY_DSN,
    },
    stripe: {
        secretKey: process.env.STRIPE_SECRET_KEY,
        [DesignerSubscription.FREE]: {
            id: process.env.STRIPE_FREE_ID,
            monthly: getArrayValue('STRIPE_FREE_MONTHLY_ID'),
            yearly: getArrayValue('STRIPE_FREE_YEARLY_ID'),
        },
        [DesignerSubscription.PLUS]: {
            id: process.env.STRIPE_PLUS_ID,
            monthly: getArrayValue('STRIPE_PLUS_MONTHLY_ID'),
            yearly: getArrayValue('STRIPE_PLUS_YEARLY_ID'),
        },
        [DesignerSubscription.PRO]: {
            id: process.env.STRIPE_PRO_ID,
            monthly: getArrayValue('STRIPE_PRO_MONTHLY_ID'),
            yearly: getArrayValue('STRIPE_PRO_YEARLY_ID'),
        },
        [CompanySubscription.FREE]: {
            id: process.env.STRIPE_COMPANY_FREE_ID,
            monthly: getArrayValue('STRIPE_COMPANY_FREE_MONTHLY_ID'),
            yearly: getArrayValue('STRIPE_COMPANY_FREE_YEARLY_ID'),
        },
        [CompanySubscription.PREMIUM]: {
            id: process.env.STRIPE_COMPANY_PREMIUM_ID,
            monthly: getArrayValue('STRIPE_COMPANY_PREMIUM_MONTHLY_ID'),
            yearly: getArrayValue('STRIPE_COMPANY_PREMIUM_YEARLY_ID'),
            coupon: {
                monthly: process.env.STRIPE_COMPANY_PREMIUM_MONTHLY_COUPON,
                yearly: process.env.STRIPE_COMPANY_PREMIUM_YEARLY_COUPON,
            },
        },
        webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    },
    ably: {
        apiKey: process.env.ABLY_API_KEY,
    },
    proxycurl: {
        apiKey: process.env.PROXYCURL_API_KEY,
    },
    zendesk: {
        url: process.env.ZENDESK_URL,
    },
    hubspot: {
        accessToken: process.env.HUBSPOT_ACCESS_TOKEN,
    },
    simulation: {
        endpoint: process.env.SIMULATION_ENDPOINT,
    },
    wireSizing: {
        endpoint: process.env.WIRE_SIZING_ENDPOINT,
    },
    dcideAiService: {
        apiKey: process.env.DCIDE_AI_API_KEY,
    },
    openai: {
        apiKey: process.env.OPENAI_API_KEY,
    },
    gemini: {
        projectId: process.env.GEMINI_PROJECT_ID,
        location: process.env.GEMINI_LOCATION,
        apiKey: process.env.GEMINI_API_KEY,
        credentialsBase64: process.env.GEMINI_CREDENTIALS_BASE64,
        model: getValue('GEMINI_MODEL', 'gemini-2.0-flash'),
    },
    aiModels: {
        default: getValue('AI_MODELS_DEFAULT', 'gpt-4.1-nano'),
        agent: getValue('AI_MODELS_AGENT', 'gpt-4.1-nano'),
        searchAgent: getValue('AI_MODELS_SEARCH_AGENT', 'o4-mini'),
        productAgent: getValue('AI_MODELS_PRODUCT_AGENT', 'o4-mini'),
        diagram: getValue('AI_MODELS_DIAGRAM', 'o4-mini'),
        compatiblity: getValue('AI_MODELS_COMPATIBILITY', 'gemini-2.0-flash-lite'),
        scoreResults: getValue('AI_MODELS_SCORE_RESULTS', 'gpt-4.1-mini'),
    },
    search: {
        vectorSearch: {
            annLimit: getValue('SEARCH_VECTOR_ANN_LIMIT', 1000),
            annCandidates: getValue('SEARCH_VECTOR_ANN_CANDIDATES', 10000),
            ennLimit: getValue('SEARCH_VECTOR_ENN_LIMIT', 1000),
            enabled: getValue('SEARCH_VECTOR_ENABLED', false),
        },
        textSearch: {
            enabled: getValue('SEARCH_TEXT_SEARCH_ENABLED', false),
            nameBoost: getValue('SEARCH_TEXT_NAME_BOOST', 3),
            productIdentifierBoost: getValue('SEARCH_TEXT_PRODUCT_IDENTIFIER_BOOST', 2),
            productSeriesBoost: getValue('SEARCH_TEXT_PRODUCT_SERIES_BOOST', 2),
            embeddingTextBoost: getValue('SEARCH_TEXT_EMBEDDING_TEXT_BOOST', 1),
            locationBoost: getValue('SEARCH_TEXT_LOCATION_BOOST', 10),
            locationPivot: getValue('SEARCH_TEXT_LOCATION_PIVOT', 1000e3),
        },
        aggregateScoreThreshold: getValue('AGGREGATE_SCORE_THRESHOLD', 0),
    },
    premiumManufacturerCutoffDate: getValue('PREMIUM_MANUFACTURER_CUTOFF_DATE', new Date('2025-01-01')),
    rePlusSalesEmail: process.env.RE_PLUS_SALES_EMAIL,
});
