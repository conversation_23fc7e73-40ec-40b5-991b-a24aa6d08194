import { Field } from 'payload';

const Address = (name = 'address'): Field => ({
    name,
    type: 'group',
    fields: [
        {
            name: 'name',
            type: 'text',
        },
        {
            type: 'row',
            fields: [
                {
                    name: 'street',
                    type: 'text',
                },
                {
                    name: 'number',
                    type: 'text',
                },
            ],
        },
        {
            type: 'row',
            fields: [
                {
                    name: 'postalCode',
                    type: 'text',
                },
                {
                    name: 'city',
                    type: 'text',
                },
            ],
        },
        {
            type: 'row',
            fields: [
                {
                    name: 'state',
                    type: 'text',
                },
                {
                    name: 'country',
                    type: 'text',
                },
            ],
        },
        {
            name: 'coordinates',
            type: 'point',
            defaultValue: [0, 0],
        },
    ],
});

export { Address };
