'use client';

import React, { useState } from 'react';

import { Button, useDocumentInfo } from '@payloadcms/ui';

const ImpersonateButton = () => {
    const [url, setUrl] = useState('');

    const document = useDocumentInfo();

    const handle = async () => {
        const secret = 'top-secret-key-nobody-will-ever-guess';
        const response = await fetch(`/api/users/${document.id}/impersonate?secret=${secret}`, {
            method: 'POST',
        });
        const { magicLink } = await await response.json();

        setUrl(magicLink);
    };

    return url ? <span>{url}</span> : <Button onClick={handle}>Impersonate</Button>;
};

export default ImpersonateButton;
