import { MigrateUpArgs } from '@payloadcms/db-mongodb';

import { getId } from '@/helpers';

export async function up({ payload }: MigrateUpArgs): Promise<void> {
    const { docs: manufacturers } = await payload.find({
        collection: 'manufacturers',
        select: {
            // @ts-expect-error
            caseStudies: true,
        },
        depth: 0,
        pagination: false,
    });

    for (const manufacturer of manufacturers) {
        // @ts-expect-error
        for (const caseStudy of manufacturer.caseStudies || []) {
            let file = null;

            if (caseStudy.files[0]) {
                file = getId(caseStudy.files[0].file);
            }

            await payload.create({
                collection: 'articles',
                data: {
                    name: caseStudy.name,
                    type: 'caseStudy',
                    company: manufacturer.id,
                    file,
                    teaser: {
                        description: caseStudy.description,
                        image: caseStudy.image,
                        button: {
                            url: caseStudy.button.url,
                        },
                    },
                },
            });
        }
    }
}

export async function down(): Promise<void> {
    // Migration code
}
