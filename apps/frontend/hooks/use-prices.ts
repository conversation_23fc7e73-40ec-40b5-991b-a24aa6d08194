import useSWRImmutable from 'swr/immutable';

import { config } from 'config';

import { ApiService } from 'services/ApiService';
import { Subscription } from '@repo/dcide-component-models';

const usePrices = () => {
    const swr = useSWRImmutable<Record<Subscription, { monthly: number; yearly: number }>>(
        `/subscriptions/prices`,
        () => ApiService.get(`${config.api.backend}/subscriptions/prices`),
    );

    return swr;
};

export { usePrices };
