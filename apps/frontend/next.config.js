/** @type {import('next').NextConfig} */

const { withSentryConfig } = require('@sentry/nextjs');

const sass = require('sass');

const nextConfig = {
    transpilePackages: ['dcide-component-models', 'dcide-sync-engine'],
    sassOptions: {
        prependData: `@use "./_mantine.scss" as *;`,
        logger: sass.Logger.silent,
    },
    productionBrowserSourceMaps: true,
    async redirects() {
        return [
            {
                source: '/manufacturers/:slug*',
                destination: '/profiles/:slug*',
                permanent: true,
            },
            {
                source: '/manufacturers/signup',
                destination: '/profiles/signup',
                permanent: true,
            },
            {
                source: '/manufacturers/signup/:id',
                destination: '/profiles/signup/:id',
                permanent: true,
            },
            {
                source: '/distributors/:slug*',
                destination: '/profiles/:slug*',
                permanent: true,
            },
            {
                source: '/distributors/signup',
                destination: '/profiles/signup',
                permanent: true,
            },
            {
                source: '/distributors/signup/:id',
                destination: '/profiles/signup/:id',
                permanent: true,
            },
            {
                source: '/products/search',
                destination: '/search',
                permanent: true,
            },
            {
                source: '/profile/:slug',
                destination: '/profiles/:slug',
                permanent: true,
            },
            {
                source: '/profile/:slug/request-access',
                destination: '/profiles/:slug/request-access',
                permanent: true,
            },
            {
                source: '/profile/:slug/grant-access',
                destination: '/profiles/:slug/grant-access',
                permanent: true,
            },
            {
                source: '/profile/:slug/preview',
                destination: '/profiles/:slug/preview',
                permanent: true,
            },
        ];
    },
};

const environment =
    process.env.VERCEL_ENV === 'production'
        ? 'production'
        : process.env.VERCEL_ENV === 'preview'
          ? 'staging'
          : 'development';

module.exports = nextConfig;
module.exports = withSentryConfig(module.exports, {
    silent: true,
    org: 'direct-energy-partners',
    project: `dcide-app-${environment}`,

    // An auth token is required for uploading source maps.
    ...(environment !== 'development' ? { authToken: process.env.SENTRY_AUTH_TOKEN } : {}),
    widenClientFileUpload: true,
    transpileClientSDK: false,
    hideSourceMaps: false,
    disableLogger: true,
});
