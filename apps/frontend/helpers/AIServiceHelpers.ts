export const AIServiceHelpers = {
    handleStream: async (
        endpoint: (params: object) => Promise<Response>,
        params: object,
    ): Promise<ReadableStream<any>> => {
        try {
            const response = await endpoint(params);

            if (!response.ok) {
                throw new Error('Endpoint response not OK');
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();

            return new ReadableStream({
                start(controller) {
                    const push = () => {
                        reader?.read().then(({ done, value }: ReadableStreamReadResult<Uint8Array>) => {
                            if (done) {
                                controller.close();
                                return;
                            }
                            const decoded = decoder.decode(value);
                            const splitValue = decoded.split('}{');

                            splitValue.forEach((dataString) => {
                                let cleanString = dataString;

                                if (!cleanString.startsWith('{')) {
                                    cleanString = `{${cleanString}`;
                                }

                                if (!cleanString.endsWith('}')) {
                                    cleanString = `${cleanString}}`;
                                }

                                let parsedString = {};

                                try {
                                    parsedString = JSON.parse(cleanString) ?? {};
                                } catch (err) {
                                    console.log(err);
                                }

                                controller.enqueue(parsedString);
                            });

                            push();
                        });
                    };

                    push();
                },
            });
        } catch (err) {
            console.error(err);
            throw err;
        }
    },

    handleStream2: async (
        endpoint: (params: object) => Promise<Response>,
        params: object,
    ): Promise<ReadableStream<string>> => {
        try {
            const response = await endpoint(params);

            if (!response.ok) {
                throw new Error('Endpoint response not OK');
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();

            return new ReadableStream({
                start(controller) {
                    const push = () => {
                        reader?.read().then(({ done, value }: ReadableStreamReadResult<Uint8Array>) => {
                            if (done) {
                                controller.close();
                                return;
                            }

                            const decoded = decoder.decode(value);

                            const lines = decoded.split('\n').filter((line) => line.startsWith('data: '));

                            lines.forEach((line) => {
                                const content = line.replace('data: ', '');
                                if (content) {
                                    controller.enqueue(content);
                                }
                            });

                            push();
                        });
                    };

                    push();
                },
            });
        } catch (err) {
            console.error(err);
            throw err;
        }
    },
};
