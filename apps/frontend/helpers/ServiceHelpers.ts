import { RouterService } from 'services/RouterService';

const ServiceHelpers = {
    createNavigator: <T extends Record<string, (...args: any[]) => string>>(urls: T) => {
        return Object.fromEntries(
            Object.entries(urls).map(([url, generator]) => [
                url,
                async (...args: Parameters<typeof generator>) => {
                    await RouterService.push(generator(...args));
                },
            ]),
        ) as {
            [K in keyof T]: (...args: Parameters<T[K]>) => Promise<void>;
        };
    },
};

export { ServiceHelpers };
