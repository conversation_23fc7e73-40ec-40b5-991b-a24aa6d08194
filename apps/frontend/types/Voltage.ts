import { z } from 'zod';
import { Voltage as VoltageValidator } from '@repo/dcide-component-models';

export type Voltage = z.infer<typeof VoltageValidator.validator>;

export const CURRENT_OS_VOLTAGES: Voltage[] = [
    {
        min: 320,
        nom: 350,
        max: 380,
        unit: 'V',
    },
    {
        min: 640,
        nom: 700,
        max: 760,
        unit: 'V',
    },
    {
        min: 1280,
        nom: 1400,
        max: 1500,
        unit: 'V',
    },
];
