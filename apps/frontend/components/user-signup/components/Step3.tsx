import { useState } from 'react';

import { Button, Stack, Title } from '@mantine/core';

import { InternalTrackingService } from 'services/InternalTrackingService';

import { CompanySignupInfo } from 'components/company-signup/components/CompanySignupInfo';

const Step3 = ({ handleNext }: { handleNext: (redirectUrl?: string) => void }) => {
    const [redirecting, setRedirecting] = useState(false);

    const onSubmit = async (createProfile: boolean) => {
        if (redirecting) {
            return;
        }

        InternalTrackingService.track('register.step3', { createProfile });

        setRedirecting(true);
        handleNext(createProfile ? '/profiles/signup' : '');
    };

    return (
        <Stack gap="md">
            <Title ta="center">Create a company profile</Title>

            <CompanySignupInfo hidePremiumBadges />

            <Stack gap={8}>
                <Button onClick={() => onSubmit(true)} loading={redirecting}>
                    Yes, I want to create a profile
                </Button>
                <Button variant="transparent" onClick={() => onSubmit(false)}>
                    I will create a profile later
                </Button>
            </Stack>
        </Stack>
    );
};

export { Step3 };
