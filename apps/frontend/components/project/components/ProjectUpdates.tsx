import React, { <PERSON> } from 'react';

import { Text, Timeline } from '@mantine/core';

import { Project } from '@repo/dcide-component-models';
import { alphabetical } from 'radash';
import { DateService } from 'services/DateService';
import { projectStatuses } from 'components/project-status';

import { useUser } from 'hooks/use-user';

const ProjectUpdates: FC<{ project: Project; isLight?: boolean }> = ({ project }) => {
    const updates = alphabetical(project.statusUpdates || [], (update) => update.date, 'desc');
    const { user: createdBy } = useUser(project.createdBy);

    return (
        <Timeline bulletSize={26} lineWidth={2}>
            {updates.map((update, index) => (
                <ProjectUpdate update={update} key={index} />
            ))}
            <Timeline.Item
                title={
                    <Text>
                        Project{' '}
                        <Text span fw={700}>
                            created
                        </Text>
                    </Text>
                }
            >
                <Text c="dimmed" size="xs">
                    {createdBy?.email} • {DateService.formatDistanceToNow(project.createdAt)}
                </Text>
            </Timeline.Item>
        </Timeline>
    );
};

const ProjectUpdate: FC<{
    update: Project['statusUpdates'][number];
}> = ({ update }) => {
    const { user } = useUser(update.user);

    return (
        <Timeline.Item
            bullet={projectStatuses[update.status]?.icon}
            title={
                <Text>
                    {update.status === 'deleted' ? (
                        <React.Fragment>
                            Project{' '}
                            <Text span fw={700}>
                                {projectStatuses[update.status]?.label}
                            </Text>
                        </React.Fragment>
                    ) : (
                        <React.Fragment>
                            Status updated to{' '}
                            <Text span fw={700}>
                                {projectStatuses[update.status]?.label}
                            </Text>
                        </React.Fragment>
                    )}
                </Text>
            }
        >
            <Text c="dimmed" size="xs">
                {user?.email} • {DateService.formatDistanceToNow(update.date)}
            </Text>
        </Timeline.Item>
    );
};

export { ProjectUpdates };
