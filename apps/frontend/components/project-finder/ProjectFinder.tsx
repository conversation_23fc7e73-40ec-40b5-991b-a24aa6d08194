import React, { FC, useState } from 'react';

import { Project } from '@repo/dcide-component-models';

import useSWR from 'swr/immutable';

import { DatasheetMantineProviderReset } from 'components/datasheet/DatasheetMantineProviderReset';
import { Button, FocusTrap, Modal, Select, Stack } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { ApiService } from 'services/ApiService';

import { useCurrentTeam } from 'hooks/use-current-team';

import { config } from 'config';

const ProjectFinder: FC<{
    onSelect: (project: Project) => void;
    children: (open: () => void) => React.ReactNode;
}> = ({ onSelect, children }) => {
    const team = useCurrentTeam();
    const [modalOpened, modalHandlers] = useDisclosure();
    const [project, setProject] = useState<Project | null>(null);

    const { data } = useSWR('/api/projects', () => {
        return ApiService.get<{
            docs: Project[];
        }>(`${config.api.backend}/projects?where[team][is]=${team?.id}&limit=100&depth=0`);
    });

    const projects = data?.docs || [];

    return (
        <>
            {children(modalHandlers.open)}
            <Modal opened={modalOpened} onClose={modalHandlers.close} withCloseButton={false} size="lg" withinPortal>
                <FocusTrap.InitialFocus />
                <DatasheetMantineProviderReset>
                    <Stack gap="xs">
                        <Select
                            data={projects.map((project) => ({
                                value: project.id,
                                label: project.name,
                            }))}
                            value={project?.id}
                            onChange={(value) => {
                                const selectedProject = projects.find((project) => project.id === value);

                                setProject(selectedProject || null);
                            }}
                            placeholder="Search for a project"
                            searchable
                            disabled={false}
                            readOnly={false}
                        />
                        <Button
                            fullWidth
                            onClick={() => {
                                onSelect(project!);
                                modalHandlers.close();
                            }}
                            disabled={!project}
                        >
                            Select project
                        </Button>
                        <Button fullWidth variant="subtle" onClick={modalHandlers.close}>
                            Cancel
                        </Button>
                    </Stack>
                </DatasheetMantineProviderReset>
            </Modal>
        </>
    );
};

export { ProjectFinder };
