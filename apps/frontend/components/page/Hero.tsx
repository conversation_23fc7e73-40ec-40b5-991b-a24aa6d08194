import React, { FC } from 'react';

import { BackgroundImage, BackgroundImageProps, Box, Text } from '@mantine/core';

import { ComponentType, all as allComponents } from '@repo/dcide-component-models';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import cx from './Hero.module.scss';

export const Hero: FC<{ children: React.ReactNode } & Partial<BackgroundImageProps>> = ({
    src = '/images/bg.jpg',
    children,
    ...props
}) => {
    return (
        <BackgroundImage src={src} ta="center" c="white" className={cx.hero} data-hero {...props}>
            <Box className={cx.content}>{children}</Box>
        </BackgroundImage>
    );
};

export const HeroComponentIcon: FC<{ type: ComponentType; selected?: boolean }> = ({ type, selected }) => {
    return (
        <Box className={cx.componentIcon} data-selected={selected}>
            <Box className={cx.icon}>
                <ComponentIcon type={type} />
            </Box>
            <Text className={cx.title}>{allComponents[type as ComponentType].plural}</Text>
        </Box>
    );
};
