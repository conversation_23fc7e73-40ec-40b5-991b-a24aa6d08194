import React from 'react';

import { IoBriefcaseOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';
import { useDesignLibraryManufacturers } from 'components/design-library/hooks/use-design-library-manufacturers';

import { InlineFilters } from './InlineFilters';

const InlineFiltersManufacturer = () => {
    const {
        filters: { manufacturer },
    } = useDesignLibrarySearch();

    const { manufacturers } = useDesignLibraryManufacturers();
    const selectedManufacturers = manufacturers.filter(({ id }) =>
        typeof manufacturer === 'string' ? id === manufacturer : manufacturer?.includes(id),
    );

    if (!selectedManufacturers.length) return null;

    return (
        <InlineFilters.SectionWithIcon
            icon={<IoBriefcaseOutline />}
            onRemove={() => DesignLibraryService.setFilter('manufacturer', undefined)}
            body={selectedManufacturers ? selectedManufacturers.map(({ name }) => name).join(', ') : undefined}
            active
        />
    );
};
export { InlineFiltersManufacturer };
