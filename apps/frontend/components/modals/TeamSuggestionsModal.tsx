import React, { FC } from 'react';

import { User } from '@repo/dcide-component-models';

import { Badge, Box, Button, Group, Space, Table } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { ModalTitle } from 'components/modals/ModalTitle';
import { ModalDescription } from 'components/modals/ModalDescription';

import { useTeam } from 'hooks/use-team';
import Link from 'next/link';
import { TeamHelpers } from 'helpers/TeamHelpers';

type Props = ContextModalProps<{
    teamSuggestions: User['teamSuggestions'];
}>;

const TeamSuggestionsModal: FC<Props> = ({ context, id, innerProps }) => {
    const { teamSuggestions = [] } = innerProps;

    const close = () => {
        context.closeModal(id);
    };

    return (
        <Box>
            <Space h="xs" />
            <ModalTitle maw="80%" textWrap="balance" mx="auto">
                {teamSuggestions.length === 1
                    ? 'Do you want to join this team?'
                    : 'Do you want to join one of these teams?'}
            </ModalTitle>
            <Space h="xs" />
            <ModalDescription ta="center">
                {teamSuggestions.length === 1
                    ? 'Based on your email address and company name, we think you might be interested in joining this team.'
                    : 'Based on your email address and company name, we think you might be interested in joining one of these teams.'}
            </ModalDescription>
            <Space h="lg" />
            <Table withTableBorder style={{ borderRadius: '4px' }}>
                <Table.Tbody>
                    {teamSuggestions.map((teamSuggestion) => (
                        <TeamSuggestion teamSuggestion={teamSuggestion} close={close} key={teamSuggestion.team} />
                    ))}
                </Table.Tbody>
            </Table>
            <Space h="xs" />
            <Button onClick={close} variant="subtle" fullWidth>
                Skip
            </Button>
        </Box>
    );
};

const TeamSuggestion: FC<{
    teamSuggestion: {
        team: string;
        reason: string;
    };
    close: () => void;
}> = ({ teamSuggestion }) => {
    const { team } = useTeam(teamSuggestion.team);

    return team ? (
        <Table.Tr>
            <Table.Td p="xs">
                <Group
                    component={Link}
                    // @ts-ignore
                    href={TeamHelpers.urls.requestAccess(team.id)}
                    onClick={close}
                    justify="space-between"
                    style={{
                        color: 'var(--mantine-color-gray-7)',
                        textDecoration: 'none',
                    }}
                >
                    {team.name}
                    <Badge size="xs" radius="xs" variant="default" style={{ cursor: 'pointer' }}>
                        Request Access
                    </Badge>
                </Group>
            </Table.Td>
        </Table.Tr>
    ) : null;
};

export { TeamSuggestionsModal };
