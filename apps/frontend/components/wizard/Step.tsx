import { Divider, Title } from '@mantine/core';
import React, { FC } from 'react';

export const Step: FC<{
    title: string;
    id: string;
    onValidate?: () => boolean;
    children?: React.ReactNode;
    back?: () => string;
    next?: () => string;
}> = ({ title, children }) => {
    return (
        <>
            <Title>{title}</Title>
            <Divider my="md" />
            <div
            //initial={{ opacity: 0 }}
            //animate={{ opacity: 1 }}
            //transition={{ duration: 0.5 }}
            >
                {children}
            </div>
        </>
    );
};
