import { useEffect, useState } from 'react';

import { Button, Group, Pagination, Select, Stack, Text, TextInput } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';

import { CompanySubscription, CompanySubscriptionConfig, Event, PublishedStatus } from '@repo/dcide-component-models';

import { DateService } from 'services/DateService';

import { Page } from 'components/page';

import { useEvents } from 'hooks/use-events';
import { useCompanyProfiles } from 'hooks/use-company-profiles';

import { CompaniesTable } from 'components/company-overview/components/CompaniesTable';
import { CompanyDataExportModal } from './CompanyDataExportModal';
import { convertFiltersToListProps } from 'components/company-overview/components/helpers/convert-filters-to-list-props';

enum SortType {
    NAME = 'name',
    CREATED_AT = '-createdAt',
}

const SortOptions = [
    { value: SortType.CREATED_AT, label: 'Newest first' },
    { value: SortType.NAME, label: 'Alphabetical' },
];

const StatusOptions = [
    { value: '', label: 'All' },
    ...Object.values(PublishedStatus).map((status) => ({ value: status, label: `Only ${status}` })),
];

const SubscriptionOptions = [
    { value: '', label: 'All' },
    ...Object.entries(CompanySubscriptionConfig).map(([key, value]) => ({
        value: key,
        label: value.title,
    })),
    { value: 'internal.premium', label: 'Internal Premium (old comapnies)' },
];

export type CompanyOverviewFilters = {
    page: number;
    sort: SortType;
    query: string;
    status: PublishedStatus | null;
    subscription: CompanySubscription | 'internal.premium' | null;
    event: Event | null;
    claimedOrInternal: 'all' | 'claimed' | 'internal';
};

const CompanyOverview = () => {
    const { events } = useEvents();

    const [{ page, sort, query, status, subscription, event, claimedOrInternal }, setFilters] =
        useState<CompanyOverviewFilters>({
            page: 1,
            sort: SortType.CREATED_AT,
            query: '',
            status: null,
            subscription: null,
            event: null,
            claimedOrInternal: 'all',
        });
    const [debouncedQuery] = useDebouncedValue(query, 500);

    const [exportOpen, setExportOpen] = useState(false);

    const listCompaniesProps = convertFiltersToListProps({
        page,
        sort,
        query: debouncedQuery,
        status,
        subscription,
        event,
        claimedOrInternal,
    });

    const { companies, mutate, isLoading, totalPages, data, totalDocs } = useCompanyProfiles(listCompaniesProps);

    const setFilter = (
        key: keyof CompanyOverviewFilters,
        value: CompanyOverviewFilters[keyof CompanyOverviewFilters],
    ) => {
        setFilters((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    useEffect(() => {
        setFilter('page', 1);
    }, [sort, query, status, subscription, event]);

    const resetFilters = () => {
        setFilter('query', '');
        setFilter('status', null);
        setFilter('subscription', null);
        setFilter('event', null);
        setFilter('claimedOrInternal', 'all');
    };

    const showResetFilters = !!status || !!event || !!subscription || !!query || claimedOrInternal !== 'all';

    return (
        <Page showBackground title="Manage companies">
            <Page.WideContent>
                <Page.Title>Manage companies</Page.Title>

                <Stack gap="xs">
                    <Group gap={4} align="end">
                        <TextInput
                            type="search"
                            value={query}
                            onChange={(event) => setFilter('query', event.currentTarget.value)}
                            label="Search by name"
                            placeholder="Search by name"
                        />
                        <Select
                            data={SortOptions}
                            value={sort}
                            onChange={(sort) => setFilter('sort', sort as SortType)}
                            label="Sort by"
                            placeholder="Sort by"
                        />
                        <Select
                            data={StatusOptions}
                            value={status ?? ''}
                            onChange={(status) => setFilter('status', status as PublishedStatus)}
                            label="Filter by status"
                            placeholder="Filter by status"
                        />
                        <Select
                            data={SubscriptionOptions}
                            value={subscription ?? ''}
                            onChange={(subscription) => setFilter('subscription', subscription as CompanySubscription)}
                            label="Filter by subscription"
                            placeholder="Filter by subscription"
                        />
                        {!!events.length && (
                            <Select
                                data={events.map((event) => ({
                                    value: event.id,
                                    label: `${event.name} (${DateService.format(event.start, 'MMM D') + ' - ' + DateService.format(event.end, 'MMM D')})`,
                                }))}
                                value={event?.id || null}
                                onChange={(eventId) =>
                                    setFilter('event', events.find((event) => event.id === eventId) || null)
                                }
                                label="Filter by event"
                                placeholder="Filter by event"
                            />
                        )}
                        <Select
                            data={[
                                { value: 'all', label: 'Claimed + internal' },
                                { value: 'claimed', label: 'Only claimed' },
                                { value: 'internal', label: 'Only internal' },
                            ]}
                            value={claimedOrInternal}
                            onChange={(claimedOrInternal) => setFilter('claimedOrInternal', claimedOrInternal)}
                            label="Claimed or internal?"
                            placeholder="Claimed or internal?"
                        />
                        {showResetFilters && (
                            <Button onClick={resetFilters} variant="transparent" c="red" ml={-10}>
                                Reset
                            </Button>
                        )}

                        <Button ml="auto" onClick={() => setExportOpen(true)}>
                            Export
                        </Button>
                    </Group>
                    <Text c="dimmed" fz="xs" fw={600}>
                        {totalDocs} companies matching the filters
                    </Text>
                </Stack>

                <Stack>
                    <CompaniesTable
                        loading={isLoading}
                        companies={companies}
                        refreshCompanies={mutate}
                        refreshCompany={(id, values) => {
                            mutate(
                                {
                                    ...data,
                                    docs: companies.map((company) => {
                                        if (company.id === id) {
                                            return {
                                                ...company,
                                                ...values,
                                            };
                                        }

                                        return company;
                                    }),
                                },
                                { revalidate: false },
                            );
                        }}
                    />
                    <Pagination
                        size="sm"
                        mt="md"
                        value={page}
                        onChange={(page) => setFilter('page', page)}
                        total={totalPages}
                    />
                </Stack>

                <CompanyDataExportModal
                    opened={exportOpen}
                    onClose={() => setExportOpen(false)}
                    filters={{
                        page,
                        sort,
                        query,
                        status,
                        subscription,
                        event,
                        claimedOrInternal,
                    }}
                />
            </Page.WideContent>
        </Page>
    );
};

export { CompanyOverview };
