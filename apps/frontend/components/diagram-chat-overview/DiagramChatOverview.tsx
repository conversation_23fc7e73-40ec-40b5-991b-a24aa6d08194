import React, { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { Button, Group, Loader, Pagination, Stack, Switch, Text } from '@mantine/core';
import { useDebouncedCallback } from '@mantine/hooks';

import { DiagramChatChannel, DiagramChatChannelListParams } from '@repo/dcide-component-models';

import { useDiagramChatChannels } from 'components/diagram-chat-overview/hooks/use-diagram-chat-channels';

import { RouterService } from 'services/RouterService';

import { DiagramChatChannelItem } from 'components/diagram-chat-overview/components/DiagramChatChannelItem';
import { DiagramChatChannelModal } from 'components/diagram-chat-overview/components/DiagramChatChannelModal';
import Link from 'next/link';

type DiagramChatChannelFilters = Partial<DiagramChatChannelListParams>;

const DiagramChatOverview: FC = () => {
    const { query } = useRouter();

    const [filters, setFilters] = useState<DiagramChatChannelFilters>({
        withoutInternal: false,
        page: 1,
    });

    const debouncedRouterUpdate = useDebouncedCallback((filters: DiagramChatChannelFilters) => {
        RouterService.setQuery('filters', JSON.stringify(filters), 'replace');
    }, 300);

    const { withoutInternal, page } = filters;

    useEffect(() => {
        const filters = query.filters ? JSON.parse(query.filters as string) : {};

        setFilters((currentFilters) => ({
            ...currentFilters,
            ...filters,
            page: filters.page ?? 1,
        }));
    }, [query.filters]);

    const diagramChatChannelFilters: DiagramChatChannelFilters = {
        page,
        withoutInternal,
    };

    const { diagramChatChannels, totalPages, totalDocs, isLoading } = useDiagramChatChannels(diagramChatChannelFilters);

    const [openedChannel, setOpenedChannel] = useState<DiagramChatChannel | null>(null);

    useEffect(() => {
        const channelId = query.diagramChatChannel;

        if (!channelId) {
            setOpenedChannel(null);
            return;
        }

        const channel = diagramChatChannels.find((channel) => channel.id === channelId);

        if (!channel) {
            setOpenedChannel(null);
            return;
        }

        setOpenedChannel(channel);
    }, [query.diagramChatChannel, diagramChatChannels]);

    if (!diagramChatChannels) return null;

    const setFilter = (
        key: keyof DiagramChatChannelFilters,
        value: DiagramChatChannelFilters[keyof DiagramChatChannelFilters],
    ) => {
        const newFilters: DiagramChatChannelFilters = {
            ...filters,
            page: 1,
            [key]: value,
        };

        setFilters(newFilters);
        debouncedRouterUpdate(newFilters);
    };

    const setPage = (newPage: number) => {
        setFilter('page', newPage);
        debouncedRouterUpdate({ ...filters, page: newPage });
    };

    const openChannel = (channel: DiagramChatChannel) => {
        RouterService.setQuery('diagramChatChannel', channel.id, 'replace');
    };

    const closeChannel = () => {
        RouterService.removeQuery('diagramChatChannel', 'replace');
    };

    return (
        <Stack gap="xs">
            <Button.Group mx="auto">
                <Button variant="outline" component={Link} href="/admin/intercom">
                    Intercom chats
                </Button>
                <Button variant="filled">Diagram chats</Button>
            </Button.Group>

            <Group>
                <Switch
                    checked={withoutInternal}
                    onChange={(e) => setFilter('withoutInternal', e.currentTarget.checked)}
                    label="Not started by internal"
                />
            </Group>

            {totalDocs === 0 ? (
                <Text c="dimmed" fz="xs">
                    No results
                </Text>
            ) : (
                <Text c="dimmed" fz="xs">
                    {totalDocs} results
                </Text>
            )}

            {isLoading && <Loader size="sm" my="md" />}

            {!isLoading &&
                diagramChatChannels.map((channel) => (
                    <DiagramChatChannelItem
                        key={channel.id}
                        channel={channel}
                        openChannel={() => openChannel(channel)}
                    />
                ))}

            {totalPages > 1 && <Pagination size="sm" mt="md" value={page} onChange={setPage} total={totalPages} />}

            {openedChannel && <DiagramChatChannelModal channel={openedChannel} closeChannel={closeChannel} />}
        </Stack>
    );
};

export { DiagramChatOverview };
