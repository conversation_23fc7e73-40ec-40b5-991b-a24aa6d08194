import { useRouter } from 'next/router';
import { useEffect } from 'react';

import { IntercomService } from 'services/IntercomService';

const useIntercomDeeplink = () => {
    const router = useRouter();
    const { query, pathname } = useRouter();

    useEffect(() => {
        if (query.action === 'intercom') {
            const { channel } = query;

            IntercomService.open(channel as string);

            const updatedQuery = { ...query };
            delete updatedQuery['action'];
            delete updatedQuery['channel'];

            router.replace({ pathname, query: updatedQuery }, undefined, { shallow: true }).then();
        }
    }, [query.action]);
};

export { useIntercomDeeplink };
