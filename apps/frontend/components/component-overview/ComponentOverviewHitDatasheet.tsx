import React, { FC, useState } from 'react';

import { Badge, Divider, Group, Stack, Text, UnstyledButton } from '@mantine/core';
import { IoChevronDown } from 'react-icons/io5';

import {
    Charger,
    ChargerConnectors,
    Component,
    energyConverter,
    VoltageType,
    powerConverter,
    voltageConverter,
    currentConverter,
} from '@repo/dcide-component-models';

import { VoltageTypeSwitch } from 'components/buttons/VoltageTypeSwitch';
import { FormatHelpers } from 'helpers/formatters';

type Port = Component['electrical']['ports'][number];
type MergedPorts = { index: number | [number, number]; port: Port }[];

const NUMBER_SHOWN_PORTS = 3;

const ComponentOverviewHitDatasheet: FC<{ component: Component }> = ({ component }) => {
    const electrical = component.electrical ?? {};
    const ports = electrical.ports;

    const energyCapacity = 'energyCapacity' in electrical ? electrical.energyCapacity : null;

    const mergedPorts: MergedPorts = ports?.reduce((mergedPorts, port, index) => {
        if (mergedPorts.length === 0) {
            return [{ index, port }];
        }

        const [{ index: lastPortIndexOrRange, port: lastPort }] = mergedPorts.splice(-1, 1);

        if (portsAreSimilar(port, lastPort)) {
            const rangeStartIndex = Array.isArray(lastPortIndexOrRange)
                ? lastPortIndexOrRange[0]
                : lastPortIndexOrRange;

            return [...mergedPorts, { index: [rangeStartIndex, index], port }];
        }

        return [...mergedPorts, { index: lastPortIndexOrRange, port: lastPort }, { index, port }];
    }, [] as MergedPorts);

    const [showExpand, setShowExpand] = useState(mergedPorts?.length > NUMBER_SHOWN_PORTS);

    const shownPorts = showExpand ? mergedPorts.slice(0, NUMBER_SHOWN_PORTS) : mergedPorts;

    if (!ports?.length && !energyCapacity?.value) {
        return null;
    }

    const parallelableCapacity = 'parallelableCapacity' in electrical ? electrical.parallelableCapacity : 1;

    return (
        <Stack gap="xs">
            <Stack gap="sm">
                {energyCapacity?.value && (
                    <Group gap="xs">
                        <Badge variant="outline" color="gray.6" radius="xs" px={6}>
                            Capacity
                        </Badge>
                        {FormatHelpers.formatMeasurement(energyCapacity, energyConverter)}
                        {parallelableCapacity > 1 ? `/unit (max ${parallelableCapacity}x units parallel)` : ''}
                    </Group>
                )}
                {shownPorts.map(({ port, index }) => (
                    <ComponentOverviewHitDatasheetPort
                        port={port}
                        index={index}
                        key={index.toString()}
                        justOne={ports?.length === 1}
                    />
                ))}
                {'charging' in electrical && <ChargingSpecifications charging={electrical.charging} />}
                {'canServeAs' in component && component.canServeAs.length > 0 && (
                    <Stack gap={4}>
                        <ComponentOverviewHitDatasheetTitle title="Can be used as" />
                        <Group gap={4}>
                            {component.canServeAs.map((componentType) => (
                                <Badge
                                    variant="outline"
                                    color="gray.6"
                                    radius="xs"
                                    px={6}
                                    size="xs"
                                    key={componentType}
                                >
                                    {componentType}
                                </Badge>
                            ))}
                        </Group>
                    </Stack>
                )}
            </Stack>
            {showExpand && (
                <Group gap={2} c="dimmed">
                    <UnstyledButton fz="sm" fw={600} onClick={() => setShowExpand(false)}>
                        More specifications
                    </UnstyledButton>
                    <IoChevronDown />
                </Group>
            )}
        </Stack>
    );
};

const ComponentOverviewHitDatasheetPort: FC<{
    port: Port;
    index: number | [number, number];
    justOne?: boolean;
}> = ({ port, index, justOne }) => {
    const hasDC = 'DC' in port && port.DC.enabled;
    const hasAC = 'AC' in port && port.AC.enabled;

    if (!hasDC && !hasAC) return null;

    const subtitle: string[] = [];

    if (port.powerFlowDirection) {
        subtitle.push(port.powerFlowDirection);
    }

    if ('purpose' in port && port.purpose) {
        subtitle.push(port.purpose);
    }

    const isPortRange = Array.isArray(index);
    const portTitle = isPortRange ? `Ports ${index[0] + 1} … ${index[1] + 1}` : `Port ${index + 1}`;
    const showTitle = !justOne || isPortRange;

    return (
        <Stack gap={'xs'}>
            {showTitle && <ComponentOverviewHitDatasheetTitle title={portTitle} subtitle={subtitle.join(' • ')} />}

            {hasDC && <PortSpecificationLine portValues={port['DC']} voltageType={'DC'} />}
            {hasAC && <PortSpecificationLine portValues={port['AC']} voltageType={'AC'} />}
        </Stack>
    );
};

const ComponentOverviewHitDatasheetTitle = ({ title, subtitle }: { title: string; subtitle?: string }) => {
    return (
        <Divider
            label={
                <Text
                    inherit
                    fw={700}
                    fz="xs"
                    tt="uppercase"
                    style={{
                        lineHeight: '1rem',
                    }}
                >
                    {title}
                    {subtitle && (
                        <Text span tt="initial" fz="xs" c="gray.5">
                            {' '}
                            ({subtitle})
                        </Text>
                    )}
                </Text>
            }
            labelPosition="left"
        />
    );
};

const PortSpecificationLine: FC<{
    portValues: any;
    voltageType: VoltageType;
}> = ({ portValues, voltageType }) => {
    const voltage = FormatHelpers.formatMinNomMax(portValues.voltage, voltageConverter);
    const power = FormatHelpers.formatMinNomMax(portValues.power, powerConverter);
    const current = FormatHelpers.formatMinNomMax(portValues.current, currentConverter);

    const specifications = [voltage, power, current].filter(Boolean);

    const specificationsMissing = specifications.length === 0;

    return (
        <Group gap={'xs'} wrap="nowrap">
            <VoltageTypeSwitch voltageType={voltageType} voltageTypes={[voltageType]} variant={'outline'} />
            <Text c={specificationsMissing ? 'dimmed' : 'inherit'} fz="sm" lh={1.2}>
                {specificationsMissing ? `${voltageType} specifications missing` : specifications.join(' • ')}
            </Text>
        </Group>
    );
};

const ChargingSpecifications = ({ charging }: { charging: Charger['electrical']['charging'] }) => {
    const subtitle = charging.connectors.map((connector) => {
        return ChargerConnectors.options.find((c) => c.value === connector)?.label || 'Unknown connector';
    });

    const numberOfConnectors = charging.numberOfConnectors;
    if (numberOfConnectors) {
        subtitle.push(`${numberOfConnectors} connectors`);
    }

    const voltage = FormatHelpers.formatMinNomMax(charging.voltage, voltageConverter);
    const power = FormatHelpers.formatMinNomMax(charging.power, powerConverter);
    const current = FormatHelpers.formatMinNomMax(charging.current, currentConverter);

    const specifications = [charging.connectors.length, voltage, power, current].filter(Boolean);

    if (specifications.length === 0) return null;

    return (
        <>
            <ComponentOverviewHitDatasheetTitle title="Charging ports" subtitle={subtitle.join(' • ')} />
            <PortSpecificationLine portValues={charging} voltageType={'DC'} />
        </>
    );
};

const voltageSpecs = ['min', 'nom', 'max'] as const;
const powerSpecs = ['nom', 'max'] as const;
const currentSpecs = ['nom', 'max'] as const;

const portsAreSimilar = (a: Port, b: Port): boolean => {
    return (
        a.powerFlowDirection === b.powerFlowDirection &&
        (('DC' in a &&
            a.DC.enabled &&
            'DC' in b &&
            b.DC.enabled &&
            voltageSpecs.every((spec) => a.DC.voltage[spec] === b.DC.voltage[spec]) &&
            // @ts-ignore
            powerSpecs.every((spec) => a.DC.power?.[spec] === b.DC.power?.[spec]) &&
            // @ts-ignore
            currentSpecs.every((spec) => a.DC.current?.[spec] === b.DC.current?.[spec])) ||
            (!portHasVoltageType(a, 'DC') && !portHasVoltageType(b, 'DC'))) &&
        (('AC' in a &&
            a.AC.enabled &&
            'AC' in b &&
            b.AC.enabled &&
            voltageSpecs.every((spec) => a.AC.voltage[spec] === b.AC.voltage[spec]) &&
            // @ts-ignore
            powerSpecs.every((spec) => a.AC.power?.[spec] === b.AC.power?.[spec]) &&
            // @ts-ignore
            currentSpecs.every((spec) => a.AC.current?.[spec] === b.AC.current?.[spec])) ||
            (!portHasVoltageType(a, 'AC') && !portHasVoltageType(b, 'AC')))
    );
};

const portHasVoltageType = (port: Port, voltageType: VoltageType) => {
    // @ts-ignore
    return voltageType in port && port[voltageType].enabled;
};

export { ComponentOverviewHitDatasheet };
