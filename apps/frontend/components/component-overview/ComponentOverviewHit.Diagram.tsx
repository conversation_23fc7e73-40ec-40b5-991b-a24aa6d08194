import React, { CSSProperties, FC, useState } from 'react';

import { Text, Title, Stack, Card, Anchor, Flex, Box, UnstyledButton, Group, Alert, Button } from '@mantine/core';
import { TbChevronRight } from 'react-icons/tb';

import Link from 'next/link';

import { useHover, useMouse } from '@mantine/hooks';
import { useDraggable } from '@dnd-kit/core';

import { useComponentMeta, useCompanyProfile } from 'hooks';
import { useCurrentUser } from 'hooks/use-current-user';

import { ComponentService } from 'services/ComponentService';

import { Component, getComponentDefinition } from '@repo/dcide-component-models';
import { DraggingType } from 'types/DraggingType';

import { asyncLoader } from 'helpers/asyncLoader';
import { deNullRef } from 'helpers/de-null-ref';

import { AIEnabledBadge } from 'elements/badge/AIEnabledBadge';
import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';

import { ComponentThumbnail } from 'components/thumbnail';
import { CompanyLogo } from 'components/company-logo';
import { ComponentDiagramButton } from 'components/component-design-button';
import { ComponentIcon } from 'components/diagram/components/diagram-component-instance/ComponentIcon';

import { DiagramComponentOverviewHitDatasheet } from './ComponentOverviewHitDatasheet.Diagram';
import { isTouchDevice } from 'helpers/isTouchDevice';
import { DraggableCategoryGridItem } from 'components/diagram/components/diagram-sidebar/sidebars/add-component-instance/CategoryGridItem';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

const IMAGE_WIDTH = 40;

type Props = {
    component: Component;
    showSpecifications?: boolean;
    setShowSpecifications?: () => void;
    showLinkButton?: boolean;
    showDisableDiagramUseMessage?: boolean;
    children?: React.ReactNode;
};

const DiagramComponentOverviewHit: FC<Props> = ({
    component,
    showLinkButton = true,
    showSpecifications = true,
    setShowSpecifications,
    showDisableDiagramUseMessage,
    children,
}) => {
    const user = useCurrentUser();

    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const { hovered, ref } = useHover();

    const { disableDiagramUse } = getComponentDefinition(component.type);

    const internalShowLinkButton = showLinkButton && user;

    const showAi = !!component?.files?.length;
    const isTeamComponent = ComponentService.isTeamComponent(component);

    const [deleting, setDeleting] = useState(false);
    const [deleted, setDeleted] = useState(false);

    const handleDelete = asyncLoader(async () => {
        await ComponentService.delete(component.id);
        setDeleted(true);
    }, setDeleting);

    if (deleted) return null;

    return (
        <Card ref={deNullRef(ref)} withBorder h="100%" pos="relative" shadow={hovered ? 'xs' : undefined} p="sm">
            <Flex align="flex-start" gap="xs">
                {/* left side */}
                <Stack gap="sm" style={{ flexGrow: 1 }}>
                    <DiagramComponentOverviewHitTitle component={component} />

                    {showSpecifications ? (
                        <DiagramComponentOverviewHitDatasheet component={component} />
                    ) : setShowSpecifications ? (
                        <Group>
                            <Anchor
                                component="button"
                                onClick={() => {
                                    setShowSpecifications();
                                }}
                            >
                                <Group gap={2}>
                                    <span>Expand specifications</span>
                                    <TbChevronRight size={14} />
                                </Group>
                            </Anchor>
                        </Group>
                    ) : null}

                    {(internalShowLinkButton || showAi) && (
                        <Group justify="between" gap="md">
                            {internalShowLinkButton && <ComponentDiagramButton component={component} />}
                            {showAi && (
                                <Group gap={4}>
                                    {showAi && (
                                        <UnstyledButton
                                            component={Link}
                                            href={`${ComponentHelpers.urls.view(component.id)}?action=chat`}
                                            target="_blank"
                                        >
                                            <AIEnabledBadge />
                                        </UnstyledButton>
                                    )}
                                </Group>
                            )}
                        </Group>
                    )}
                </Stack>

                {/* right side */}
                <Stack gap="md">
                    {manufacturer && <CompanyLogo logos={manufacturer.logos} width={IMAGE_WIDTH} />}

                    <ComponentThumbnail
                        showEmpty
                        component={component}
                        style={{
                            flexShrink: 0,
                            flexGrow: 0,
                            width: IMAGE_WIDTH,
                            height: IMAGE_WIDTH,
                        }}
                    />
                </Stack>
            </Flex>

            {isTeamComponent && (
                <Box
                    style={{
                        position: 'absolute',
                        bottom: 'var(--mantine-spacing-sm)',
                        right: 'var(--mantine-spacing-sm)',
                    }}
                >
                    <SettingsDropdown loading={deleting}>
                        <SettingsDropdown.Edit
                            component={Link}
                            href={ComponentHelpers.urls.view(component.id)}
                            target="_blank"
                        >
                            Edit
                        </SettingsDropdown.Edit>
                        <SettingsDropdown.Delete onClick={handleDelete}>Delete</SettingsDropdown.Delete>
                    </SettingsDropdown>
                </Box>
            )}

            {children}

            {showDisableDiagramUseMessage && disableDiagramUse && (
                <Alert color="orange" fz="xs" p="xs" mt="xs">
                    Component cannot be used in diagrams
                </Alert>
            )}
        </Card>
    );
};

const DiagramComponentOverviewHitTitle: FC<{ component: Component }> = ({ component }) => {
    const { uniqueName, subtitle } = useComponentMeta(component);

    return (
        <Box>
            <Anchor component={Link} href={ComponentHelpers.urls.view(component.id)} target="_blank">
                <Title order={3} fz="md" fw={700} style={{ display: 'inline' }}>
                    {uniqueName}
                </Title>
            </Anchor>
            <Text c="gray.5" fz="sm" fw={600}>
                {subtitle}
            </Text>
        </Box>
    );
};

const DesktopDraggableDiagramComponentOverviewHit: FC<Props> = ({ component, ...props }) => {
    const { x, y } = useMouse();

    const { disableDiagramUse } = getComponentDefinition(component.type);

    const { attributes, listeners, setNodeRef, isDragging } = useDraggable({
        id: `draggable-new-${component.type}-${component.id}`,
        data: {
            type: DraggingType.NEW_COMPONENT_INSTANCE,
            componentType: component.type,
            componentId: component.id,
            specifications: { electrical: component.electrical },
        },
        disabled: disableDiagramUse,
    });

    const draggingStyles: CSSProperties = isDragging
        ? {
              position: 'fixed',
              left: x - 30,
              top: y - 30,

              zIndex: 9999,
          }
        : {};

    return (
        <Box>
            {isDragging && (
                <DiagramComponentOverviewHit component={component} {...props} showDisableDiagramUseMessage />
            )}

            <Box
                ref={setNodeRef}
                {...attributes}
                {...listeners}
                style={{
                    cursor: 'move',
                    userSelect: 'none',

                    ...draggingStyles,
                }}
            >
                {isDragging ? (
                    <ComponentIcon component={component} />
                ) : (
                    <DiagramComponentOverviewHit component={component} {...props} showDisableDiagramUseMessage />
                )}
            </Box>
        </Box>
    );
};

const TouchDraggableDiagramComponentOverviewHit: FC<Props> = ({ component, ...props }) => {
    const { disableDiagramUse } = getComponentDefinition(component.type);

    if (disableDiagramUse) {
        return <DiagramComponentOverviewHit component={component} {...props} showDisableDiagramUseMessage />;
    }

    return (
        <DiagramComponentOverviewHit component={component} {...props} showDisableDiagramUseMessage>
            <Group gap={8} mt="xs">
                <Box w={60} h={60}>
                    <DraggableCategoryGridItem
                        withDragHandle={false}
                        type={component.type}
                        dragOptions={{
                            id: `draggable-new-${component.type}-${component.id}`,
                            data: {
                                type: DraggingType.NEW_COMPONENT_INSTANCE,
                                componentType: component.type,
                                componentId: component.id,
                                specifications: { electrical: component.electrical },
                            },
                        }}
                    />
                </Box>
                <Text c="dimmed" fz="xs">
                    Drag onto diagram
                </Text>
            </Group>
        </DiagramComponentOverviewHit>
    );
};

const DraggableDiagramComponentOverviewHit: FC<Props> = (props) => {
    const isTouch = isTouchDevice();

    return isTouch ? (
        <TouchDraggableDiagramComponentOverviewHit {...props} />
    ) : (
        <DesktopDraggableDiagramComponentOverviewHit {...props} />
    );
};

export { DiagramComponentOverviewHit, DraggableDiagramComponentOverviewHit };
