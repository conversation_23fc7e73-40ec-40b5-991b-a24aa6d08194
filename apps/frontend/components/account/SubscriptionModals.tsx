import React, { useEffect, useState, FC } from 'react';
import { useRouter } from 'next/router';

import { Button, Modal, Text, ThemeIcon } from '@mantine/core';
import { TbCircleCheck } from 'react-icons/tb';

import cx from './SubscriptionModals.module.scss';
import { IoRocketSharp } from 'react-icons/io5';

enum Actions {
    SUBSCRIPTION_CREATED = 'subscription-created',
    SUBSCRIPTION_UPDATED = 'subscription-updated',
}

const SubscriptionChangeNotice: FC = () => {
    const router = useRouter();
    const action = router.query.action as Actions | undefined;

    const [modal, setModal] = useState<Actions | null>(null);

    const close = () => {
        setModal(null);
    };

    useEffect(() => {
        if (action) {
            setModal(action);

            const [path] = router.asPath.split('?');
            router.replace(`${path}#team`, undefined, { shallow: false }).then();
        }
    }, [action]);

    const content = modal
        ? {
              [Actions.SUBSCRIPTION_CREATED]: {
                  Icon: TbCircleCheck,
                  title: 'Subscription created',
                  description: (
                      <>
                          Thank you for subscribing!
                          <br />
                          Now go and explore all the features we have to offer.
                      </>
                  ),
              },
              [Actions.SUBSCRIPTION_UPDATED]: {
                  Icon: TbCircleCheck,
                  title: 'Subscription updated',
                  description: (
                      <>
                          Thank you for updating your subscription!
                          <br />
                          Now go and explore all the features we have to offer.
                      </>
                  ),
              },
          }[modal]
        : null;

    return (
        <Modal opened={!!modal} onClose={close} withCloseButton={false} centered>
            {content && (
                <div className={cx.notice}>
                    <ThemeIcon variant="gradient" size={60} radius={99}>
                        <IoRocketSharp size={60 / 2} />
                    </ThemeIcon>
                    <Text className={cx.title} variant="gradient">
                        {content.title}
                    </Text>
                    <Text className={cx.description}>{content.description}</Text>
                    <div className={cx.actions}>
                        <Button onClick={close}>Close</Button>
                    </div>
                </div>
            )}
        </Modal>
    );
};

export { SubscriptionChangeNotice };
