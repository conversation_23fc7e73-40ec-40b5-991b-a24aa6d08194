import { But<PERSON>, Checkbox, Divider, Flex, Stack, Text } from '@mantine/core';

import {
    CompanySubscription,
    CompanySubscriptionConfig,
    DesignerSubscription,
    getDesignerSubscriptionData,
} from '@repo/dcide-component-models';

import { PriceTag } from '../PriceTag';
import { SubscribeModalContents } from '../SubscribeModalContents';
import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../../SubscriptionUpdateContext';
import { useSubmitSubscriptionUpdate } from '../useSubmitSubscriptionUpdate';

import { SubscriptionUpdateAction } from '../../types';

export const SubscribeForCompany = ({ toSubscription }: { toSubscription: CompanySubscription }) => {
    const dispatch = useSubscriptionUpdateDispatch();
    const submitSubscriptionUpdate = useSubmitSubscriptionUpdate();
    const { toBillingCycle, numberOfSeats, isDowngrade, additionalSubscriptions, loading, team, interestedIn, isFree } =
        useSubscriptionUpdateState();

    if (toSubscription === CompanySubscription.NONE) {
        throw new Error('Cannot subscribe to NONE subscription');
    }

    const { title, subtitle } = CompanySubscriptionConfig[toSubscription];

    const subscription = getDesignerSubscriptionData(team.subscriptions)?.subscription ?? DesignerSubscription.FREE;

    const showUpgrades = subscription === DesignerSubscription.FREE;

    return (
        <SubscribeModalContents
            title={isDowngrade ? `Downgrade to ${subtitle}` : isFree ? `Sign up for ${title}` : `Upgrade to ${title}`}
            subscriptionLabel={title}
            SubmitButton={
                <Button variant="gradient" size="md" onClick={submitSubscriptionUpdate} loading={loading}>
                    {isDowngrade ? 'Change plan' : 'Continue'}
                </Button>
            }
            description={
                toSubscription === CompanySubscription.FREE ? (
                    <>
                        We ask for your credit card to securely verify your account—
                        <b>your card will not be charged at this time</b>. You can always manage your payment
                        information in your profile for future use.
                    </>
                ) : null
            }
        >
            {showUpgrades && (
                <>
                    <Divider label="Recommended Upgrades" labelPosition="left" />
                    <Stack>
                        <Flex align="flex-start" justify="space-between">
                            <Checkbox
                                label={
                                    <Text span>
                                        {numberOfSeats}x <strong>Plus subscription</strong> to DCIDE
                                    </Text>
                                }
                                description="Design and collaborate in real-time with peers and manufactures on content-rich renewable energy AC & DC microgrid reference designs."
                                checked={additionalSubscriptions.includes(DesignerSubscription.PLUS)}
                                onChange={() => {
                                    dispatch({
                                        type: SubscriptionUpdateAction.TOGGLE_ADDITIONAL_SUBSCRIPTION,
                                        payload: DesignerSubscription.PLUS,
                                    });
                                }}
                            />
                            <PriceTag subscription={DesignerSubscription.PLUS} billingCycle={toBillingCycle} />
                        </Flex>
                    </Stack>
                </>
            )}
            <Divider label="Exhibit at RE+" labelPosition="left" />
            <Text fz="sm">
                RE+ Events is the global event and association management organization specializing in the clean energy
                industry. RE+ Events host renewable energy events, covering solar, storage, hydrogen, EVs, microgrids,
                and more. It hosts multiple industry gatherings throughout the year, culminating in its annual flagship
                conference in Las Vegas.
            </Text>
            <Flex align="flex-start" justify="space-between">
                <Checkbox
                    label={<Text span>I am interested in a booth at the next RE+ Event</Text>}
                    description="A representative from RE+ Events will contact you with details on pricing and availability."
                    checked={interestedIn.includes('RE+ booth')}
                    onChange={() => {
                        dispatch({
                            type: SubscriptionUpdateAction.TOGGLE_INTERESTED_IN,
                            payload: 'RE+ booth',
                        });
                    }}
                />
            </Flex>
            <Flex align="flex-start" justify="space-between">
                <Checkbox
                    label={<Text span>I am interested in RE+ Events sponsorship opportunities</Text>}
                    description="A representative from RE+ Events will contact you with details on pricing and availability."
                    checked={interestedIn.includes('RE+ sponsorship opportunities')}
                    onChange={() => {
                        dispatch({
                            type: SubscriptionUpdateAction.TOGGLE_INTERESTED_IN,
                            payload: 'RE+ sponsorship opportunities',
                        });
                    }}
                />
            </Flex>
        </SubscribeModalContents>
    );
};
