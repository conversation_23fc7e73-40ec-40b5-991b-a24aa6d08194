import { Anchor, SegmentedControl, Text } from '@mantine/core';

import { SubscriptionBillingCycle } from '@repo/dcide-component-models';
import { useSubscriptionUpdateDispatch, useSubscriptionUpdateState } from '../SubscriptionUpdateContext';
import { SubscriptionUpdateAction } from '../types';

export const SubscriptionBillingCycleControl = () => {
    const { toBillingCycle, currentBillingCycle } = useSubscriptionUpdateState();
    const dispatch = useSubscriptionUpdateDispatch();

    const billingCycleAlreadySet = currentBillingCycle !== undefined;

    if (billingCycleAlreadySet) {
        const currentCycleTypeString =
            currentBillingCycle === SubscriptionBillingCycle.MONTHLY ? 'a monthly' : 'an annual';
        const otherCycleTypeString =
            currentBillingCycle === SubscriptionBillingCycle.MONTHLY ? 'an annual' : 'a monthly';

        return (
            <Text fz="sm">
                You are presently on{' '}
                <Text span inherit fw={600}>
                    {currentCycleTypeString}
                </Text>{' '}
                billing cycle.{' '}
                <Anchor href="mailto:<EMAIL>" inherit>
                    Contact us
                </Anchor>{' '}
                to switch to {otherCycleTypeString} billing cycle.
            </Text>
        );
    }

    return (
        <SegmentedControl
            value={toBillingCycle}
            onChange={(value) => {
                dispatch({
                    type: SubscriptionUpdateAction.SET_BILLING_CYCLE,
                    payload: value as SubscriptionBillingCycle,
                });
            }}
            data={[
                {
                    value: SubscriptionBillingCycle.MONTHLY,
                    label: 'Billed Monthly',
                },
                {
                    value: SubscriptionBillingCycle.YEARLY,
                    label: 'Billed Annually',
                },
            ]}
            disabled={billingCycleAlreadySet}
        />
    );
};
