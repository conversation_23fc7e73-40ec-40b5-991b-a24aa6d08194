import React, { useState } from 'react';

import { Box, Combobox, Group, ScrollArea, useCombobox } from '@mantine/core';

import Mention, { MentionOptions } from '@tiptap/extension-mention';
import { PluginKey } from '@tiptap/pm/state';

import { BsRobot } from 'react-icons/bs';

import cx from '../TipTapComposer.module.scss';

type MentionSuggestion = {
    id: string;
    type?: 'user' | 'ai';
    label: string;
    group?: string;
};

const useTipTapMentions = ({
    key,
    trigger,
    suggestions = [],
    render,
}: {
    key: string;
    trigger: string;
    suggestions?: MentionSuggestion[];
    render?: MentionOptions['renderHTML'];
}) => {
    const [mentionController, setMentionController] = useState<any>(null);
    const [query, setQuery] = useState('');

    const combobox = useCombobox({
        opened: mentionController,
        onDropdownClose: () => {
            setMentionController(null);
        },
    });

    const configuration: Partial<MentionOptions> = {
        suggestion: {
            pluginKey: new PluginKey(key),
            char: trigger,
            items: ({ query }) => {
                setQuery(query);

                return [];
            },
            render: () => ({
                onStart: (props) => {
                    setMentionController(props);

                    combobox.selectFirstOption();
                },
                onKeyDown(props) {
                    const { key } = props.event;

                    if (key === 'ArrowDown') {
                        combobox.selectNextOption();

                        return true;
                    }

                    if (key === 'ArrowUp') {
                        combobox.selectPreviousOption();

                        return true;
                    }

                    if (key === 'Enter' || key === 'Tab') {
                        combobox.clickSelectedOption();
                        props.event.stopPropagation();

                        return true;
                    }

                    if (key === 'Escape') {
                        setMentionController(null);
                    }

                    return false;
                },
                onExit() {
                    setMentionController(null);
                },
            }),
        },
    };

    if (render) {
        configuration.renderHTML = render;
    }

    const extension = Mention.extend({ name: key }).configure(configuration);

    const filterSuggestion = (suggestion: { id: string; label: string }) => {
        return suggestion.label.toLowerCase().includes(query.toLowerCase());
    };

    const groupless: MentionSuggestion[] = [];
    const groups = suggestions.filter(filterSuggestion).reduce(
        (groups, suggestion) => {
            const { group } = suggestion;

            if (group) {
                groups[group] = groups[group] || [];
                groups[group].push(suggestion);
            } else {
                groupless.push(suggestion);
            }

            return groups;
        },
        {} as {
            [key: string]: MentionSuggestion[];
        },
    );

    const component = (
        <Combobox
            store={combobox}
            onOptionSubmit={(id) => {
                const option = suggestions.find((suggestion) => suggestion.id === id)!;

                const { command, editor, range } = mentionController!;

                editor.commands.deleteRange({ from: range.to, to: range.to + query.length });
                command(option);
            }}
        >
            <Combobox.Target>
                <div
                    style={{
                        position: 'absolute',
                        left: 0,
                        top: 0,

                        width: '100%',
                        height: '100%',

                        pointerEvents: 'none',
                    }}
                />
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options>
                    <ScrollArea.Autosize mah={200}>
                        {groupless.map((suggestion) =>
                            suggestion.id === 'ai' ? (
                                <Combobox.Option value={suggestion.id} key={suggestion.id}>
                                    <Group gap="xs">
                                        <BsRobot width={16} height={16} />
                                        <span>{suggestion.id}</span>
                                        <Box
                                            className={cx.mentionAILabel}
                                            style={{ marginLeft: 'auto', marginRight: 0 }}
                                        >
                                            AI assistant
                                        </Box>
                                    </Group>
                                </Combobox.Option>
                            ) : (
                                <Combobox.Option value={suggestion.id} key={suggestion.id}>
                                    {suggestion.label}
                                </Combobox.Option>
                            ),
                        )}
                        {Object.entries(groups).map(([group, groupSuggestions]) => (
                            <Combobox.Group label={group} key={group}>
                                {groupSuggestions.map((suggestion) => (
                                    <Combobox.Option value={suggestion.id} key={suggestion.id}>
                                        {suggestion.label}
                                    </Combobox.Option>
                                ))}
                            </Combobox.Group>
                        ))}
                    </ScrollArea.Autosize>
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );

    return {
        extension,
        component,
    };
};

export { useTipTapMentions };
