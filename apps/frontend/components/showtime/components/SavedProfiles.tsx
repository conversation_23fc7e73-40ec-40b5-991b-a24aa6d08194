import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { <PERSON><PERSON>, Button, Card, Flex } from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { BsPencilFill } from 'react-icons/bs';
import { <PERSON><PERSON><PERSON><PERSON>, IoSparkles } from 'react-icons/io5';

import { useCompanyProfile } from 'hooks/use-company-profile';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { ShowtimeData } from 'services/ShowtimeService';

import { IconWithText } from 'elements/IconWithText';
import { GridSection } from 'components/section/GridSection';
import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { InAppSupportBadge } from 'elements/badge/InAppSupportBadge';
import { CompanyProfileTeaser } from 'components/company-profile-teaser/CompanyProfileTeaser';

const SavedProfiles: FC = () => {
    const [showtimeData] = useLocalStorage<ShowtimeData[] | undefined>({
        key: ShowtimeHelpers.localStorageKey.showtimeData,
    });

    return (
        <>
            {!showtimeData?.length && (
                <EmptyMessage>
                    You are not following any profiles yet
                    <Button component={Link} href={RouterHelpers.urls.searchTab('profiles')}>
                        Browse profiles
                    </Button>
                </EmptyMessage>
            )}

            <GridSection nbCols={1}>
                {showtimeData?.map((profile) => (
                    <SavedProfile
                        key={profile.company}
                        profileId={profile.company}
                        contact={profile.contact}
                        note={profile.note}
                    />
                ))}
            </GridSection>
        </>
    );
};

const SavedProfile = ({
    profileId,
    contact,
    note,
}: { profileId: string } & Partial<Pick<ShowtimeData, 'contact' | 'note'>>) => {
    const { company } = useCompanyProfile(profileId);

    if (!company) {
        return null;
    }

    return (
        <CompanyProfileTeaser company={company} showProductsPreview showActions={false}>
            {(contact?.length || note) && (
                <>
                    <Card bg="yellow.0">
                        {contact?.length && (
                            <IconWithText
                                icon={<IoPerson />}
                                iconColor="yellow.9"
                                text={`Talked to ${contact?.join(', ')}`}
                                textProps={{ fz: 'sm' }}
                            />
                        )}
                        {note && (
                            <IconWithText
                                icon={<BsPencilFill />}
                                iconColor="yellow.9"
                                text={note}
                                textProps={{ fz: 'sm' }}
                            />
                        )}
                        {note && (
                            <IconWithText
                                icon={<IoSparkles />}
                                iconColor="brand.4"
                                text={
                                    <Anchor inherit c="brand.4" fw={500} td="underline">
                                        Summarize my notes
                                    </Anchor>
                                }
                                textProps={{ fz: 'sm' }}
                            />
                        )}
                    </Card>
                </>
            )}
            <Flex gap={4}>
                <Button
                    fullWidth
                    variant="outline"
                    size="xs"
                    component={Link}
                    href={CompanyProfileHelpers.urls.view(company.slug)}
                >
                    View profile
                </Button>
                <Button
                    fullWidth
                    variant="outline"
                    size="xs"
                    component={Link}
                    href={CompanyProfileHelpers.urls.view(company.slug, 'products')}
                >
                    View products
                </Button>
                <InAppSupportBadge company={company} fullWidth variant="outline" size="xs" label="Chat" />
            </Flex>
        </CompanyProfileTeaser>
    );
};

export { SavedProfiles };
