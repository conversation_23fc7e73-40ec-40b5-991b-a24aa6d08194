import React, { FC, useEffect } from 'react';

import { Card, Flex, Group, Stack, Text, Title } from '@mantine/core';
import { useSessionStorage } from '@mantine/hooks';

import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';

import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page/Page';

import { ReplusLogo } from 'components/logo/ReplusLogo';
import { LoginButton } from 'components/header/components/LoginButton';
import { SavedProfiles } from 'components/showtime/components/SavedProfiles';
import { SaveProfileModal } from 'components/showtime/components/SaveProfileModal';

const Showtime: FC = () => {
    const user = useCurrentUser();

    const [, setShowShowtimeNav] = useSessionStorage<boolean>({
        key: ShowtimeHelpers.localStorageKey.showShowtimeNav,
    });

    useEffect(() => {
        setShowShowtimeNav(true);
    }, []);

    return (
        <Page
            title="Showtime"
            showBackground
            hideFooter
            hideLicenseAgreement
            breadcrumbs={{ type: 'floating.fullWidth' }}
        >
            <Page.Hero pb={50} mb={-40}>
                <Group gap="xs">
                    <ReplusLogo isWhite width={50} />

                    <Stack gap={0} align="start">
                        <Title fw={800}>Showtime</Title>
                        <Text fw={500} c="dimmed" fz="sm">
                            Find and save your favorite profiles
                        </Text>
                    </Stack>
                </Group>
            </Page.Hero>

            <Page.Content maw={800}>
                <Stack>
                    {!user && (
                        <Card className="gradient-background-light">
                            <Flex style={{ opacity: 0.9 }} justify="space-between" align="center">
                                <Text fw={600}>Save your favorites — and don’t lose your notes.</Text>
                                <LoginButton
                                    buttonProps={{ size: 'sm', variant: 'gradient', style: { flexShrink: 0 } }}
                                >
                                    Secure My RE+ List
                                </LoginButton>
                            </Flex>
                        </Card>
                    )}

                    <SavedProfiles />

                    <SaveProfileModal />
                </Stack>
            </Page.Content>
        </Page>
    );
};

export { Showtime };
