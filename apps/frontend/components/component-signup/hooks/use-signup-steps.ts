import { ComponentType } from '@repo/dcide-component-models';

import { Step1 } from 'components/component-signup/steps/Step1';
import { StepAI } from 'components/component-signup/steps/StepAI';
import { StepFiles } from 'components/component-signup/steps/StepFiles';
import { StepCompatible } from 'components/component-signup/steps/StepCompatible';
import { StepDatasheet } from 'components/component-signup/steps/StepDatasheet';

const useSignupSteps = (componentType?: ComponentType) => {
    if (componentType === 'other') {
        return [Step1, StepFiles, StepCompatible];
    }

    if (componentType === 'cable') {
        return [Step1, StepFiles, StepDatasheet, StepCompatible];
    }

    return [Step1, StepFiles, StepAI, StepCompatible];
};

export { useSignupSteps };
