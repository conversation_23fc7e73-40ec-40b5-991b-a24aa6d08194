import { Component, ComponentSection } from '@repo/dcide-component-models';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet/DatasheetMode';
import { useComponentContext } from './use-component-context';
import { valueHasNonNullNonUnitValue } from 'helpers/not-null-helpers';

export enum ComponentSectionVisibility {
    HIDDEN,
    VISIBLE,
}

export const useComponentSectionVisbility = (section?: ComponentSection) => {
    const mode = useDatasheetMode();
    const { component } = useComponentContext();

    if (!section) {
        return ComponentSectionVisibility.VISIBLE;
    }

    if (mode !== DatasheetMode.VIEW) {
        return ComponentSectionVisibility.VISIBLE;
    }

    if (!(section in component)) {
        return ComponentSectionVisibility.HIDDEN;
    }

    if (componentHasDataForSection(component, section)) {
        return ComponentSectionVisibility.VISIBLE;
    }

    return ComponentSectionVisibility.HIDDEN;
};

export const componentHasDataForSection = (component: Component, section: ComponentSection): boolean | null => {
    switch (section) {
        case ComponentSection.GENERAL:
            return true;

        case ComponentSection.COMMUNICATION:
            return 'communication' in component ? valueHasNonNullNonUnitValue(component.communication) : false;

        case ComponentSection.PROJECTS:
            return 'projects' in component ? valueHasNonNullNonUnitValue(component.projects) : false;

        case ComponentSection.IMAGES:
            return component.images.some((image) => image.file !== null);

        case ComponentSection.ELECTRICAL:
        case ComponentSection.PERFORMANCE:
        case ComponentSection.STANDARDS:
        case ComponentSection.MECHANICAL:
        case ComponentSection.ENVIRONMENTAL:
        case ComponentSection.QUESTIONS:
        case ComponentSection.COMPATIBLE_WITH:
        case ComponentSection.FILES:
            return valueHasNonNullNonUnitValue(component[section]);

        default:
            return null;
    }
};
