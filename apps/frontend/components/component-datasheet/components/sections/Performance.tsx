import React from 'react';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';
import { EfficiencyField } from 'components/component-fields/EfficiencyField';
import { PowerLossesField } from 'components/component-fields/PowerLossesField';
import { StandbyPowerField } from 'components/component-fields/StandbyPowerField';
import { AiAssistantProductComponent } from '../AiAssistantProductComponent';
import { ComponentSection } from '@repo/dcide-component-models';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

import { useComponentContext } from '../../hooks/use-component-context';

const Performance = () => {
    const { component } = useComponentContext();
    const performance = component.performance || {};

    if (!ComponentHelpers.componentTypeHasPerformance(component.type)) {
        return null;
    }

    return (
        <ComponentDatasheetSection
            id={ComponentSection.PERFORMANCE}
            title="Performance"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.PERFORMANCE} />}
        >
            <Datasheet.Table header={{ label: 'Performance' }}>
                {'efficiency' in performance && (
                    <Datasheet.TableRow name="performance.efficiency" label="Efficiency">
                        <EfficiencyField name="performance.efficiency" fields={['nom', 'max']} />
                        <AiAssistantProductComponent name={`performance.efficiency`} />
                    </Datasheet.TableRow>
                )}
                {'losses' in performance && (
                    <Datasheet.TableRow name="performance.losses" label="Losses">
                        <PowerLossesField name="performance.losses" />
                        <AiAssistantProductComponent name={`performance.losses`} />
                    </Datasheet.TableRow>
                )}
                {'standbyPower' in performance && (
                    <Datasheet.TableRow name="performance.standbyPower" label="Stand-by Power">
                        <StandbyPowerField name="performance.standbyPower" />
                        <AiAssistantProductComponent name={`performance.standbyPower`} />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
        </ComponentDatasheetSection>
    );
};

export { Performance };
