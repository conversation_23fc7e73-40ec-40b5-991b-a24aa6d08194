import React, { FC } from 'react';
import { Component, ComponentSection } from '@repo/dcide-component-models';

import { Accordion, Box, Button, Stack, Text, MantineProvider, ActionIcon, Flex, Alert, Group } from '@mantine/core';
import { IoAdd, IoChatbubblesOutline, IoTrashOutline } from 'react-icons/io5';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { TextField } from 'components/forms/fields/TextField';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

import { IntercomService } from 'services/IntercomService';

import { useController } from 'react-hook-form';
import { useCompanyProfile } from 'hooks/use-company-profile';

import cx from './Questions.module.scss';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

const Questions: FC<{
    component: Component;
}> = ({ component }) => {
    const mode = useDatasheetMode();
    const editable = mode === DatasheetMode.EDIT;

    if (!component.questions?.length && !editable) {
        return null;
    }

    return (
        <ComponentDatasheetSection id={ComponentSection.QUESTIONS} title="FAQ">
            {editable ? <Edit /> : <View component={component} />}
        </ComponentDatasheetSection>
    );
};

const View: FC<{ component: Component }> = ({ component }) => {
    const { questions = [] } = component;
    const { company } = useCompanyProfile(component.manufacturer);

    const [first] = questions;
    const inAppSupport = CompanyProfileHelpers.offersInAppSupport(company);

    const openIntercom = () => {
        IntercomService.open();
    };

    return (
        <React.Fragment>
            {questions.length > 0 && (
                <Accordion multiple variant="contained" chevronSize={12} defaultValue={[first.question]}>
                    {questions.map((question) => (
                        <Accordion.Item value={question.question} key={question.question}>
                            <Accordion.Control>
                                <Text fw={600} fz="sm">
                                    {question.question}
                                </Text>
                            </Accordion.Control>
                            <Accordion.Panel>
                                <Text maw="100ch" fz="sm">
                                    {question.answer}
                                </Text>
                            </Accordion.Panel>
                        </Accordion.Item>
                    ))}
                </Accordion>
            )}

            {inAppSupport && (
                <Alert variant="light" color="primary">
                    <Group>
                        {questions.length > 0 ? (
                            <Text fz="sm" style={{ flexGrow: 1 }}>
                                <Text inherit fw={600}>
                                    Didn&apos;t find the answer you were looking for?
                                </Text>
                                You can always start a conversation with {company?.name}.
                            </Text>
                        ) : (
                            <Text fz="sm" style={{ flexGrow: 1 }}>
                                <Text inherit fw={600} component="span">
                                    This product doesn&apos;t have any answered questions yet.
                                </Text>
                                You can always start a conversation with {company?.name}.
                            </Text>
                        )}
                        <Button
                            size="xs"
                            variant="gradient"
                            style={{ pointerEvents: 'all' }}
                            onClick={openIntercom}
                            leftSection={<IoChatbubblesOutline size={16} />}
                        >
                            Start a conversation with {company?.name}
                        </Button>
                    </Group>
                </Alert>
            )}
        </React.Fragment>
    );
};

const Edit = () => {
    const { field } = useController({
        name: 'questions',
    });

    const questions: Component['questions'] = field.value || [];

    const addQuestion = () => {
        field.onChange([...questions, { question: '', answer: '' }]);
    };

    return (
        <MantineProvider
            theme={{
                components: {
                    Input: {
                        styles: () => ({
                            input: {
                                border: '1px solid var(--mantine-color-gray-2)',
                            },
                        }),
                    },
                },
            }}
        >
            <Stack gap="xs">
                <Text>Add questions and answers that your customers might be interested in about this product.</Text>

                {questions.map((question, index) => (
                    <EditRow index={index} key={`${question.question}-${index}`} />
                ))}
                <Box>
                    <Button size="xs" variant="outline" onClick={addQuestion} leftSection={<IoAdd />}>
                        Add a question and answer
                    </Button>
                </Box>
            </Stack>
        </MantineProvider>
    );
};

const EditRow: FC<{ index: number }> = ({ index }) => {
    const { field } = useController<Component>({
        name: 'questions',
    });

    const deleteQuestion = () => {
        const questions: Component['questions'] = field.value;

        field.onChange([...questions.slice(0, index), ...questions.slice(index + 1)]);
    };

    return (
        <Flex gap="xs">
            <Stack className={cx.editQuestionWrapper} gap="xs">
                <TextField name={`questions.${index}.question`} label="Question" />
                <MultilineTextField name={`questions.${index}.answer`} label="Answer" autosize />
            </Stack>
            <ActionIcon size="md" variant="light" color="red" onClick={deleteQuestion}>
                <IoTrashOutline />
            </ActionIcon>
        </Flex>
    );
};

export { Questions };
