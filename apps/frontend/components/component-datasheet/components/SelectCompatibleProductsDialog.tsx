import { Button, Group, Modal } from '@mantine/core';
import { Component } from '@repo/dcide-component-models';
import { useTeamProducts } from 'hooks/use-team-products';

type Props = {
    opened: boolean;
    close: () => void;
    component: Component;
    onSuccess?: () => void;
};

export const SelectCompatibleProductsDialog = ({ opened, close }: Props) => {
    const { products } = useTeamProducts();

    console.log('products', products);

    return (
        <Modal opened={opened} withCloseButton onClose={close} size="lg" title="Select Compatible Products">
            Select the products you want to connect with this product.
            {products.map((product) => (
                <div key={product.id}>
                    <input type="checkbox" id={product.id} />
                    <label htmlFor={product.id}>{product.name}</label>
                </div>
            ))}
            {/* Todo: allow people to select multiple products (they have access to) for connection. This should work across multiple teams. */}
            <Group justify="center" p="md">
                <Button
                    onClick={() => {
                        console.log('Connecting...');
                        close();
                    }}
                >
                    Connect
                </Button>
            </Group>
        </Modal>
    );
};
