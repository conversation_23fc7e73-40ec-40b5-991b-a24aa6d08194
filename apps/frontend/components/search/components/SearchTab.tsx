import { Box, Center } from '@mantine/core';
import { useScrollIntoView } from '@mantine/hooks';

import cx from './SearchTab.module.scss';

type Props = {
    filters: React.ReactNode;
    body: React.ReactNode;
    pagination?: (props: { scrollToTop: () => void }) => React.ReactNode;
};

export const SearchTab = ({ filters, body, pagination }: Props) => {
    const { scrollIntoView: scrollToTop, targetRef } = useScrollIntoView<HTMLDivElement>({
        // Large number to scroll to the top of the page
        offset: 1024,
    });

    return (
        <>
            <Box className={cx.filters} ref={targetRef}>
                {filters}
            </Box>

            {body}

            {pagination && (
                <Center py="md">
                    {pagination({
                        scrollToTop,
                    })}
                </Center>
            )}
        </>
    );
};
