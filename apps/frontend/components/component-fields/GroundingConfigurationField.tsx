import React, { <PERSON> } from 'react';

import { GroundingConfigurationOptions } from '@repo/dcide-component-models';
import { SelectFieldProps, SelectField } from 'components/forms/fields/SelectField';

type GroundingConfigurationFieldProps = Omit<SelectFieldProps, 'data'> & {
    name: string;
    voltageType: 'AC' | 'DC' | null;
};

const GroundingConfigurationField: FC<GroundingConfigurationFieldProps> = ({ voltageType, ...props }) => {
    const options = GroundingConfigurationOptions[voltageType ?? 'all'];

    return <SelectField {...props} data={options} clearable={false} />;
};

export { GroundingConfigurationField };
