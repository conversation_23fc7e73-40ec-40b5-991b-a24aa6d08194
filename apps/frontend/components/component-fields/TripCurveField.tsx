import React, { <PERSON> } from 'react';

import { TripCurves } from '@repo/dcide-component-models';
import { MultiSelectFieldProps, MultiSelectField } from 'components/forms/fields/MultiSelectField';

type TripCurveFieldProps = Omit<MultiSelectFieldProps, 'data'> & {
    name: string;
};

const TripCurveField: FC<TripCurveFieldProps> = (props) => <MultiSelectField {...props} data={TripCurves.options} />;

export { TripCurveField };
