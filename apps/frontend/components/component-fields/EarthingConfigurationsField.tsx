import React from 'react';

import { ACEarthingConfigurations, DCEarthingConfigurations, VoltageType } from '@repo/dcide-component-models';

import { MultiSelectFieldProps, MultiSelectField } from 'components/forms/fields/MultiSelectField';

type Props = Omit<MultiSelectFieldProps, 'data'> & { voltageType: VoltageType };

const EarthingConfigurationsField = ({ voltageType, ...props }: Props) => (
    <MultiSelectField
        {...props}
        data={voltageType === 'DC' ? DCEarthingConfigurations.options : ACEarthingConfigurations.options}
    />
);

export { EarthingConfigurationsField };
