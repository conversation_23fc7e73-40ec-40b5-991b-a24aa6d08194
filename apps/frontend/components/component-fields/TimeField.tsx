import React from 'react';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    MeasurementInputWrapper,
    useRenderVariableMultiInput,
} from 'components/forms/fields/measurement';

import { MeasurementFieldType, timeConverter } from '@repo/dcide-component-models';

type TimeFieldProps = { name: string; fields?: MeasurementFieldType[] } & MeasurementInputWrapperProps;

const TimeField = ({ name, fields = ['value'], ...wrapperProps }: TimeFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({ name, converter: timeConverter });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;

    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            size: wrapperProps.size,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { TimeField };
