import React, { FC } from 'react';

import { MeasurementSystem, WireSize } from '@repo/dcide-component-models';

import { ComboboxItem } from '@mantine/core';
import { SelectFieldProps, SelectField } from 'components/forms/fields/SelectField';

type WireSizeFieldProps = Omit<SelectFieldProps, 'data'> & {
    name: string;
    min?: number;
    format?: MeasurementSystem;
};

const data: ComboboxItem[] = WireSize.options.map((option) => ({
    value: option.value,
    label: option.label,
    // @ts-ignore exists.
    mm2: option.mm2,
    // @ts-ignore exists.
    awg: option.awg,
}));

const WireSizeField: FC<WireSizeFieldProps> = ({ min, format, ...props }) => {
    let options = data;

    if (min) {
        options = options.map((option) => {
            return {
                ...option,
                disabled: +option.value < min,
            };
        });
    }

    if (format === MeasurementSystem.METRIC) {
        options = options.map((option) => {
            return {
                ...option,
                // @ts-ignore
                label: option.mm2 + ' mm²',
            };
        });
    }

    if (format === MeasurementSystem.IMPERIAL) {
        options = options.map((option) => {
            return {
                ...option,
                // @ts-ignore
                label: option.awg + ' AWG',
            };
        });
    }

    return <SelectField {...props} data={options} placeholder="None" searchable />;
};

export { WireSizeField };
