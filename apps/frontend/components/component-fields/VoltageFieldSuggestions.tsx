import React, { <PERSON> } from 'react';

import { useFormContext } from 'react-hook-form';

import { Combobox, Text } from '@mantine/core';

import {
    AC_EUROPE_VOLTAGES,
    AC_USA_VOLTAGES,
    CURRENT_OS_VOLTAGES,
    Voltage,
    VoltageType,
    voltageConverter,
} from '@repo/dcide-component-models';

import { FormatHelpers } from 'helpers/formatters';
import { VoltageFieldProps } from './VoltageField';

const VoltageFieldSuggestions: FC<
    Pick<VoltageFieldProps, 'name' | 'fields'> & {
        recentVoltages?: Voltage[];
        suggestedZoneVoltage?: Voltage;
        voltageType?: VoltageType | null;
    }
> = ({ name, suggestedZoneVoltage, recentVoltages = [], fields = ['min', 'nom', 'max'], voltageType }) => {
    const { setValue } = useFormContext();

    const handleClick = (voltages: Voltage) => {
        setValue(name, extractShownFields(voltages, fields));
    };

    const getFormattedVoltages = (voltages: Voltage) =>
        FormatHelpers.formatMinNomMax(extractShownFields(voltages, fields), voltageConverter);

    return (
        <Combobox.Dropdown data-voltage-suggestions-dropdown style={{ minWidth: '8rem' }}>
            <Combobox.Options>
                {suggestedZoneVoltage ? (
                    <Combobox.Group label="Suggested Zone Voltage">
                        <Combobox.Option
                            value={Object.values(suggestedZoneVoltage).join('-')}
                            onClick={() => handleClick(suggestedZoneVoltage)}
                        >
                            {getFormattedVoltages(suggestedZoneVoltage)}
                        </Combobox.Option>
                    </Combobox.Group>
                ) : null}

                {(!voltageType || voltageType === 'DC') && (
                    <Combobox.Group label="CurrentOS & EMerge Alliance">
                        {CURRENT_OS_VOLTAGES.map(({ label, ...voltages }, index) => (
                            <Combobox.Option
                                value={Object.values(voltages).join('-')}
                                key={index}
                                onClick={() => handleClick(voltages)}
                            >
                                {getFormattedVoltages(voltages)}
                                {label && (
                                    <Text inherit span c="dimmed">
                                        ({label})
                                    </Text>
                                )}
                            </Combobox.Option>
                        ))}
                    </Combobox.Group>
                )}

                {(!voltageType || voltageType === 'AC') && (
                    <>
                        <Combobox.Group label="Europe">
                            {AC_EUROPE_VOLTAGES.map(({ label, ...voltages }, index) => (
                                <Combobox.Option
                                    value={Object.values(voltages).join('-')}
                                    key={index}
                                    onClick={() => handleClick(voltages)}
                                >
                                    {getFormattedVoltages(voltages)}{' '}
                                    {label && (
                                        <Text inherit span c="dimmed">
                                            ({label})
                                        </Text>
                                    )}
                                </Combobox.Option>
                            ))}
                        </Combobox.Group>
                        <Combobox.Group label="United States">
                            {AC_USA_VOLTAGES.map(({ label, ...voltages }, index) => (
                                <Combobox.Option
                                    value={Object.values(voltages).join('-')}
                                    key={index}
                                    onClick={() => handleClick(voltages)}
                                >
                                    {getFormattedVoltages(voltages)}{' '}
                                    {label && (
                                        <Text inherit span c="dimmed">
                                            ({label})
                                        </Text>
                                    )}
                                </Combobox.Option>
                            ))}
                        </Combobox.Group>
                    </>
                )}

                <Combobox.Group label="Recently used">
                    {recentVoltages.slice(0, 6).map((voltages, index) => (
                        <Combobox.Option
                            value={Object.values(voltages).join('-')}
                            key={index}
                            onClick={() => handleClick(voltages)}
                        >
                            {getFormattedVoltages(voltages)}
                        </Combobox.Option>
                    ))}
                </Combobox.Group>
            </Combobox.Options>
        </Combobox.Dropdown>
    );
};

const extractShownFields = (voltage: Voltage, fields: string[]) =>
    Object.fromEntries(Object.entries(voltage).filter(([key]) => key === 'unit' || fields?.includes(key)));

export { VoltageFieldSuggestions };
