import React from 'react';

import { ResistancePerUnitLengthConverter } from '@repo/dcide-component-models';
import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    MeasurementInputWrapper,
    MeasurementInput,
    UnitWithTooltip,
} from 'components/forms/fields/measurement';
import { useDefaultMeasurementSystem } from 'hooks/use-default-measurement-system';

type ResistancePerLengthFieldProps = { name: string } & MeasurementInputWrapperProps;

const ResistancePerLengthField = ({ name, ...wrapperProps }: ResistancePerLengthFieldProps) => {
    const measurementSystem = useDefaultMeasurementSystem();
    const converter = new ResistancePerUnitLengthConverter(measurementSystem);

    const {
        unit,
        cycleUnit,
        fields: { value },
    } = useMultiUnitMeasurementController({ name, converter });

    return (
        <MeasurementInputWrapper {...wrapperProps}>
            <MeasurementInput
                {...value}
                placeholder="Resistance per length"
                unit={<UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>}
                size={wrapperProps.size}
            />
        </MeasurementInputWrapper>
    );
};

export { ResistancePerLengthField };
