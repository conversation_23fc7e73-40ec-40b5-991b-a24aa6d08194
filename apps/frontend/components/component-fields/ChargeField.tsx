import React from 'react';

import { TbBatteryCharging } from 'react-icons/tb';

import { UnitWithTooltip } from 'components/forms/fields/measurement/Unit';
import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    MeasurementInputWrapper,
    MeasurementInput,
} from 'components/forms/fields/measurement';
import { chargeConverter } from '@repo/dcide-component-models';

type ChargeFieldProps = { name: string; hideIcons?: boolean } & MeasurementInputWrapperProps;

const ChargeField = ({ name, hideIcons, ...wrapperProps }: ChargeFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: { value },
    } = useMultiUnitMeasurementController({
        name,
        converter: chargeConverter,
        defaultUnit: 'Ah',
    });

    return (
        <MeasurementInputWrapper {...wrapperProps}>
            <MeasurementInput
                {...value}
                icon={hideIcons ? null : <TbBatteryCharging size={20} strokeWidth={1.25} />}
                unit={<UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>}
            />
        </MeasurementInputWrapper>
    );
};

export { ChargeField };
