import React, { <PERSON> } from 'react';

import { Button } from '@mantine/core';
import { IoShareOutline } from 'react-icons/io5';

import { useCopyShareUrl } from 'hooks/use-copy-share-url';

const ShareButton: FC<{ type: 'product' | 'profile'; label?: string }> = ({ type, label = 'Share' }) => {
    const { clipboard, share } = useCopyShareUrl(type);

    return (
        <Button size="compact-xs" variant="subtle" onClick={share} leftSection={<IoShareOutline size={12} />}>
            {clipboard.copied ? 'Copied url' : label}
        </Button>
    );
};

export { ShareButton };
