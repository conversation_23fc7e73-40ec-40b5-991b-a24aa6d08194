import React, { FC, useEffect, useState } from 'react';

import { Stack } from '@mantine/core';
import { BsBox, BsP<PERSON>cil, BsPeople, BsPersonVcard } from 'react-icons/bs';
import { IoFolderOpenOutline } from 'react-icons/io5';

import { UserProgress, UserProgressItem } from '@repo/dcide-component-models';

import { UserService } from 'services/UserService';

import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { Dashboard } from 'components/dashboard/Dashboard';
import { GridSection } from 'components/section/GridSection';
import { useUserProgress } from 'components/sidebar-nav/hooks/useUserProgress';

import { ChecklistItem, ChecklistItemIconPosition } from 'components/checklist-item/ChecklistItem';

const getUrlForItem = (item: UserProgressItem) => {
    switch (item) {
        case UserProgressItem.CREATE_PROJECT:
            return ProjectHelpers.urls.create();
        case UserProgressItem.INVITE_TEAM_USERS:
            return '/account#team';
        case UserProgressItem.CREATE_PROFILE:
            return CompanyProfileHelpers.urls.create();
        case UserProgressItem.CREATE_REFERENCE_DESIGN:
            return ProjectHelpers.urls.create({
                name: '[reference design]',
                isReferenceDesign: true,
            });
        case UserProgressItem.CREATE_PRODUCT:
            return ComponentHelpers.urls.create();
        default:
            return '#';
    }
};

const getIconForItem = (item: UserProgressItem) => {
    switch (item) {
        case UserProgressItem.CREATE_PROJECT:
            return { icon: <IoFolderOpenOutline />, iconPosition: ChecklistItemIconPosition.SW };
        case UserProgressItem.INVITE_TEAM_USERS:
            return { icon: <BsPeople />, iconPosition: ChecklistItemIconPosition.S };
        case UserProgressItem.CREATE_PROFILE:
            return { icon: <BsPersonVcard />, iconPosition: ChecklistItemIconPosition.E };
        case UserProgressItem.CREATE_REFERENCE_DESIGN:
            return { icon: <BsPencil />, iconPosition: ChecklistItemIconPosition.NE };
        case UserProgressItem.CREATE_PRODUCT:
            return { icon: <BsBox />, iconPosition: ChecklistItemIconPosition.NE };
        default:
            return { icon: undefined, iconPosition: undefined };
    }
};

const DashboardChecklist: FC = () => {
    const { progress } = useUserProgress();

    const shownProgress = progress?.filter((item) => item.show);

    const [hadUncompleted, setHadUncompleted] = useState(false);

    useEffect(() => {
        if (hadUncompleted) return;

        setHadUncompleted(shownProgress?.some((item) => !item.completed));
    }, [hadUncompleted, shownProgress]);

    if (!shownProgress?.length) return null;

    const firstUncompleted = shownProgress.findIndex((item) => !item.completed);
    const completedAll = firstUncompleted === -1;

    if (!hadUncompleted && completedAll) return null;

    return (
        <Stack>
            <Dashboard.Title>Your first time here?</Dashboard.Title>
            <GridSection
                nbCols={6}
                cols={{
                    xs: 2,
                    sm: 4,
                    md: 4,
                    xl: 6,
                    xxl: 8,
                }}
            >
                {shownProgress.map((item, index) => (
                    <DashboardChecklistItem key={index} item={item} />
                ))}

                <ChecklistItem.Finish mounted={completedAll} />
            </GridSection>
        </Stack>
    );
};

const DashboardChecklistItem = ({ item }: { item: UserProgress }) => {
    const { mutate } = useUserProgress();

    const handleComplete = async (item: UserProgressItem) => {
        await UserService.completeProgressItem(item);
        await mutate?.();
    };

    return (
        <ChecklistItem
            href={getUrlForItem(item.key)}
            value={item.key}
            completed={item.completed}
            label={item.label}
            handleComplete={handleComplete}
            {...getIconForItem(item.key)}
        />
    );
};

export { DashboardChecklist };
