import React from 'react';

import Link from 'next/link';

import { Bad<PERSON>, Button } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { Dashboard } from 'components/dashboard/Dashboard';
import { PROFILE_SIGNUP_ITEMS } from 'components/company-signup/components/CompanySignupInfo';

const DashboardCompanySignupInfo = () => {
    return (
        <Dashboard.HighlightedInfo
            items={PROFILE_SIGNUP_ITEMS.map(({ premium, ...item }) => ({
                ...item,
                rightSection: premium ? (
                    <Badge px={5} size="xs" radius="xs">
                        Premium
                    </Badge>
                ) : undefined,
            }))}
            title="Do not forget to create your company profile"
        >
            <Button
                component={Link}
                href={CompanyProfileHelpers.urls.create()}
                leftSection={<IoAdd />}
                style={{ alignSelf: 'center' }}
            >
                Create a company profile
            </Button>
        </Dashboard.HighlightedInfo>
    );
};

export { DashboardCompanySignupInfo };
