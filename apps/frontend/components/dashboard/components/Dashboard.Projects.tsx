import React, { <PERSON> } from 'react';
import Link from 'next/link';

import { Button, Stack } from '@mantine/core';
import { IoAdd, IoFolderOpenOutline } from 'react-icons/io5';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { useRecentProjects } from 'hooks/use-recent-projects';

import { Dashboard } from 'components/dashboard/Dashboard';
import { ProjectGrid } from 'components/project-overview/components';

const DashboardProjects: FC = () => {
    const recentProjects = useRecentProjects();

    return (
        <Stack gap="xs">
            <Dashboard.Title
                rightSection={
                    <>
                        <Button
                            size="compact-xs"
                            variant="transparent"
                            leftSection={<IoAdd />}
                            component={Link}
                            href={ProjectHelpers.urls.create()}
                        >
                            Create project
                        </Button>
                        <Button
                            size="compact-xs"
                            variant="transparent"
                            leftSection={<IoFolderOpenOutline />}
                            component={Link}
                            href={ProjectHelpers.urls.overview()}
                        >
                            All projects
                        </Button>
                    </>
                }
            >
                Recent projects
            </Dashboard.Title>

            {recentProjects?.length ? (
                <ProjectGrid
                    projects={recentProjects}
                    cols={{
                        xs: 1,
                        lg: 2,
                        xl: 3,
                    }}
                />
            ) : (
                <Dashboard.EmptyProjects />
            )}
        </Stack>
    );
};

export { DashboardProjects };
