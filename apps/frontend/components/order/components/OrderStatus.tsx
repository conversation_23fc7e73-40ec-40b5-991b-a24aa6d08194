import React, { FC, useState } from 'react';

import { Button, ButtonProps, Flex, FlexProps, Group, Text } from '@mantine/core';

import { OrderController, useOrder } from 'hooks';
import { Order, OrderStatus as OrderStatusType } from '@repo/dcide-component-models';
import { orderStatuses } from 'components/order-status';
import { TbArrowRight } from 'react-icons/tb';

type StatusActions = {
    [key in OrderStatusType]?: {
        main?: OrderStatusType;
        secondary?: OrderStatusType[];
    };
};

const reviewerActions = ['quoteSubmitted', 'orderAcknowledged', 'orderInProgress', 'orderCompleted'];

const statusActions: StatusActions = {
    draft: {
        main: 'quoteRequested',
    },
    quoteRequested: {
        main: 'quoteSubmitted',
        secondary: ['draft'],
    },
    quoteSubmitted: {
        main: 'orderSubmitted',
        secondary: ['draft'],
    },
    orderSubmitted: {
        main: 'orderAcknowledged',
        secondary: ['orderCancelled'],
    },
    orderAcknowledged: {
        main: 'orderInProgress',
        secondary: ['orderCancelled'],
    },
    orderInProgress: {
        main: 'orderCompleted',
    },
    orderCancelled: {
        main: 'draft',
    },
};

const OrderStatus: FC<{ order: Order; showSecondaryActions?: boolean; showLabels?: boolean } & FlexProps> = ({
    order: serverSideOrder,
    showLabels = true,
    showSecondaryActions = true,
    ...props
}) => {
    const orderController = useOrder(serverSideOrder.id, { fallback: serverSideOrder, depth: 1 });

    if (orderController.order === null) {
        return null;
    }

    const status = orderController.order.status;

    return (
        <Flex align="center" justify="flex-end" gap={8} mb="xs" ml="auto" {...props}>
            <Text fw={700} fz="sm">
                {showLabels && (
                    <Text span c="dimmed" fz="inherit" fw={500}>
                        Status:{' '}
                    </Text>
                )}

                {orderStatuses[status]?.label}
            </Text>
            <OrderActions controller={orderController} showSecondaryActions={showSecondaryActions} />
        </Flex>
    );
};

const OrderActions: FC<{ controller: OrderController; showSecondaryActions?: boolean } & ButtonProps> = ({
    controller,
    showSecondaryActions,
    ...props
}) => {
    const status = controller.order.status;
    const nextStatus = statusActions[status]?.main;
    const secondaryActions = showSecondaryActions ? statusActions[status]?.secondary : [];

    const isReviewer = controller.order.permissions?.includes('order.review');

    if (nextStatus && isReviewer && !reviewerActions.includes(nextStatus)) return null;
    if (!nextStatus && !secondaryActions?.length) return null;

    return (
        <React.Fragment>
            <TbArrowRight size={16} />
            <Group gap={4}>
                <MainOrderAction controller={controller} {...props} />
                {secondaryActions?.map((action) => (
                    <OrderAction key={action} status={action} controller={controller} variant="outline" {...props} />
                ))}
            </Group>
        </React.Fragment>
    );
};

const MainOrderAction: FC<{ controller: OrderController } & ButtonProps> = ({ controller, ...props }) => {
    const status = controller.order.status;
    const nextStatus = statusActions[status]?.main;

    const isReviewer = controller.order.permissions?.includes('order.review');

    if (!nextStatus) return null;
    if (isReviewer && !reviewerActions.includes(nextStatus)) return null;

    return <OrderAction status={nextStatus} controller={controller} {...props} />;
};

const OrderAction: FC<{ status: OrderStatusType; controller: OrderController } & ButtonProps> = ({
    status,
    controller,
    ...props
}) => {
    const [loading, setLoading] = useState(false);

    const isReviewer = controller.order.permissions?.includes('order.review');

    const changeStatus = async () => {
        setLoading(true);
        await controller.eventTriggers[status]();
        setLoading(false);
    };

    if (!isReviewer && reviewerActions.includes(status)) return null;

    return (
        <Button size="compact-sm" onClick={changeStatus} disabled={controller.isEmpty} {...props} loading={loading}>
            {orderStatuses[status]?.action}
        </Button>
    );
};

export { OrderStatus, MainOrderAction };
