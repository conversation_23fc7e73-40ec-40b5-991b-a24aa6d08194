import React, { <PERSON> } from 'react';

import { IoFolderOpenOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';

import { ProjectService } from 'services/ProjectService';
import { useShowDesignEditor } from 'hooks/use-show-design-editor';

const SidebarNavDesignEditor: FC = () => {
    const user = useCurrentUser();
    const showDesignEditor = useShowDesignEditor();

    if (!user) {
        return null;
    }

    return (
        <SidebarNav.Section>
            <SidebarNav.Item
                href={ProjectHelpers.urls.overview()}
                icon={<IoFolderOpenOutline />}
                rightSection={
                    <SidebarNav.CreateButton
                        tooltip="Create project"
                        onClick={() => ProjectService.navigate.create()}
                    />
                }
                hidden={!showDesignEditor}
            >
                My DCIDE Projects
            </SidebarNav.Item>
        </SidebarNav.Section>
    );
};

export { SidebarNavDesignEditor };
