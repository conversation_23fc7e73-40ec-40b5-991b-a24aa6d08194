import useSWRImmutable from 'swr/immutable';

import { UserProgress, UserProgressItem } from '@repo/dcide-component-models';

import { useCurrentUser } from 'hooks/use-current-user';

import { UserService } from 'services/UserService';
import { UserHelpers } from 'helpers/UserHelpers';

import { prepareConditionalSwrArgs } from 'hooks/prepare-conditional-swr-args';

const manufacturerItems: UserProgressItem[] = [
    UserProgressItem.CREATE_PROFILE,
    UserProgressItem.CREATE_PRODUCT,
    UserProgressItem.CREATE_REFERENCE_DESIGN,
];

const useUserProgress = () => {
    const currentUser = useCurrentUser();

    const swr = useSWRImmutable(
        ...prepareConditionalSwrArgs({
            key: UserHelpers.swr.progress(),
            fetcher: UserService.getProgress,
            condition:
                // only refetch if user has uncompleted progress items
                !!currentUser && currentUser.completedProgressItems?.length !== Object.values(UserProgressItem).length,
        }),
    );

    const progress = (swr.data ?? []) as UserProgress[];
    const shownProgress = currentUser?.isManufacturer
        ? progress.filter((item) => manufacturerItems.includes(item.key))
        : progress;

    return {
        ...swr,
        progress: shownProgress,
    };
};

export { useUserProgress };
