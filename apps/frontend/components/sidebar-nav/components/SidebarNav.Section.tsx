import React from 'react';

import { Stack } from '@mantine/core';

import { SidebarNav } from 'components/sidebar-nav/SidebarNav';
import { CollapsibleSection } from 'components/collapsible-section/CollapsibleSection';

const SidebarNavSection = ({
    storageKey,
    icon,
    title,
    children,
}: {
    storageKey?: string;
    icon?: React.ReactNode;
    title?: string;
    children: React.ReactNode;
}) => {
    if (storageKey && title) {
        return (
            <CollapsibleSection
                storageKey={storageKey}
                title={<SidebarNav.Title icon={icon}>{title}</SidebarNav.Title>}
            >
                <Stack gap="xs">{children}</Stack>
            </CollapsibleSection>
        );
    }

    return (
        <Stack gap="xs">
            {title && <SidebarNav.Title>{title}</SidebarNav.Title>}
            {children}
        </Stack>
    );
};

export { SidebarNavSection };
