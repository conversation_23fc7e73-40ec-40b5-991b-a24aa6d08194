import React, { HTMLAttributeAnchorTarget } from 'react';

import Link from 'next/link';
import { useRouter } from 'next/router';

import { Box, Collapse, MantineColor, MantineStyleProps, UnstyledButton } from '@mantine/core';

import cx from './SidebarNav.Item.module.scss';

export type SidebarNavItemProps = {
    icon?: React.ReactNode;
    href?: string;
    target?: HTMLAttributeAnchorTarget;
    onClick?: () => void;
    color?: MantineColor;
    children: React.ReactNode;
    rightSection?: React.ReactNode;
    hidden?: boolean;
    w?: MantineStyleProps['w'];
};

const SidebarNavItem = ({
    icon,
    href,
    target,
    onClick,
    color = 'gray',
    children,
    rightSection,
    hidden,
    w,
}: SidebarNavItemProps) => {
    const router = useRouter();

    const props: any = {
        'className': cx.item,
        'data-color': color,
    };

    if (href) {
        props['data-active'] = href === router.asPath;
        props.component = Link;
        props.href = href;
        props.target = target;
    } else {
        props.onClick = onClick;
    }

    return (
        <Collapse in={!hidden}>
            <UnstyledButton {...props}>
                <Box className={cx.leftSection}>
                    {icon && <Box className={cx.icon}>{icon}</Box>}
                    <Box className={cx.text} w={w}>
                        <span>{children}</span>
                    </Box>
                </Box>

                {rightSection && <Box className={cx.rightSection}>{rightSection}</Box>}
            </UnstyledButton>
        </Collapse>
    );
};

export { SidebarNavItem };
