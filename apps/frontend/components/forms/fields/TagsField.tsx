import { useState } from 'react';
import { useController } from 'react-hook-form';

import { Kbd, TagsInput, TagsInputProps, Text } from '@mantine/core';
import { unique } from 'radash';

export type Props = TagsInputProps & {
    name: string;
};

const TagsField = ({ name, defaultValue, data = [], ...props }: Props) => {
    const {
        field,
        fieldState: { isTouched, error },
    } = useController({ name, defaultValue });

    const [searchValue, setSearchValue] = useState('');

    const renderTagsInputOption: TagsInputProps['renderOption'] = ({ option }) => {
        if (option.value === searchValue) {
            return (
                <Text fz="sm">
                    <Kbd size="xs">↵ Enter</Kbd> or click here to add <b>&apos;{searchValue}&apos;</b>
                </Text>
            );
        }

        if (option.value === '$placeholder') {
            return <Text fz="sm">Start typing to search or add your own...</Text>;
        }

        return option.value;
    };

    return (
        <TagsInput
            {...field}
            {...props}
            acceptValueOnBlur
            data={unique([
                ...(!searchValue
                    ? [
                          {
                              value: '$placeholder',
                              disabled: true,
                          },
                      ]
                    : []),
                ...(searchValue ? [searchValue] : []),
                ...data,
            ])}
            error={isTouched && error?.message}
            styles={{ input: { lineHeight: '1em' } }}
            comboboxProps={{ shadow: 'md', offset: 4 }}
            onSearchChange={setSearchValue}
            renderOption={renderTagsInputOption}
        />
    );
};

export { TagsField };
