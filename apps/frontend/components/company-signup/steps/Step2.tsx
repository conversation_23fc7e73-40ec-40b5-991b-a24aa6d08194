import { config } from 'config';

import { unique } from 'radash';
import React, { useState } from 'react';

import { Button, Stack, Title, Text } from '@mantine/core';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';

import { CompanyProfile, CompanyService, PublishedStatus, UserFeatureFlags } from '@repo/dcide-component-models';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { Form, FormOnSubmit } from 'components/forms/Form';

import { SignupLayout } from 'components/signup-layout/SignupLayout';
import { CompanySignupProps } from 'components/company-signup/CompanySignup';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { InAppSupportFields } from 'components/company-profile/components/InAppSupportFields';
import { useWatch } from 'react-hook-form';
import { ImagePreview } from 'components/image-preview/ImagePreview';
import { LocalNotificationService } from 'services/LocalNotificationService';

type Props = Omit<CompanySignupProps, 'progress' | 'active'>;

const Step2 = ({ prevStep, nextStep, company: incomingCompany }: Props) => {
    const { company, mutate } = useCompanyProfile(incomingCompany ?? null);
    const { mutate: mutateCompanies } = useCurrentTeamCompanies();

    const user = useCurrentUser();
    const team = useCurrentTeam();
    const canSubscribe = config.companyProfileSubscriptions ?? false;

    const [loading, setLoading] = useState(false);

    const [services, users] = useWatch({ name: ['services', 'users'] }) as [
        CompanyProfile['services'],
        CompanyProfile['users'],
    ];

    const handleFinishSignup = async (status: CompanyProfile['status'], proceed = true) => {
        if (!company) return;

        const hasInAppSupportUser = !services.includes(CompanyService.IN_APP_SUPPORT) || users.length > 0;
        if (!hasInAppSupportUser) {
            LocalNotificationService.showError({ message: 'Please select at least one user to notify.' });
            return;
        }

        setLoading(true);

        await CompanyProfileService.update(company.id, { status, services, users });

        await mutate?.();
        await mutateCompanies();

        if (proceed) {
            if (canSubscribe) {
                nextStep();
            } else {
                await CompanyProfileService.navigate.view(company.slug);

                if (team) {
                    CompanyProfileService.finishLocalSignup(team.id);
                }
            }
        }

        setLoading(false);
    };

    if (!company) return null;

    return (
        <>
            <SignupLayout.Content maw={700}>
                <Stack gap="xl">
                    <Stack>
                        <Stack gap="xs">
                            <Title order={3} size="h2" fw={600}>
                                Lead Management
                            </Title>

                            <Text>
                                RE+Source PRO enables a direct communication channel between you and your customers.
                            </Text>
                        </Stack>

                        <ImagePreview
                            withShadow
                            src="/images/lead_management_preview.png"
                            alt="Lead Management Preview"
                        />
                    </Stack>

                    <InAppSupportFields company={company} hideEnableCheckbox />
                </Stack>
            </SignupLayout.Content>
            <SignupLayout.Actions
                leftActions={
                    <Button variant="outline" onClick={prevStep} leftSection={<IoArrowBack />}>
                        Back
                    </Button>
                }
                rightActions={
                    user?.flags?.includes(UserFeatureFlags.OEM_SUBSCRIPTION) ? (
                        <>
                            <Button
                                onClick={() => handleFinishSignup(PublishedStatus.DRAFT, false)}
                                variant="outline"
                                loading={loading}
                            >
                                Save
                            </Button>
                            <Button
                                onClick={() => handleFinishSignup(PublishedStatus.DRAFT)}
                                rightSection={<IoArrowForward />}
                            >
                                Save and proceed
                            </Button>
                        </>
                    ) : (
                        <>
                            <Button
                                onClick={() => handleFinishSignup(PublishedStatus.DRAFT)}
                                variant="outline"
                                loading={loading}
                            >
                                {canSubscribe ? 'Continue to subscription' : 'Continue to profile'}
                            </Button>
                        </>
                    )
                }
            />
        </>
    );
};

const WrappedStep2 = (props: Props) => {
    const user = useCurrentUser();

    const { company } = useCompanyProfile(props.company ?? null);

    const onSubmit: FormOnSubmit<CompanyProfile> = async (values) => {
        if (company) {
            await CompanyProfileService.update(company.id, values);
        }
    };

    if (!company) return null;

    return (
        <Form
            onSubmit={onSubmit}
            defaultValues={{
                services: unique([...company.services, CompanyService.IN_APP_SUPPORT]),
                users: company.users.length ? company.users : user ? [user.id] : [],
            }}
            data-content-wrapper
        >
            <Step2 {...props} />
        </Form>
    );
};

export { WrappedStep2 as Step2 };
