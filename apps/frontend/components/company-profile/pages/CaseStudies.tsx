import { FC } from 'react';

import { CompanyProfile } from '@repo/dcide-component-models';

import { Card } from '@mantine/core';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { CaseStudies as CaseStudiesSection } from 'components/company-profile/sections/CaseStudies';

import cx from './styles.module.scss';

const CaseStudies: FC<{ company: CompanyProfile }> = ({ company }) => (
    <Card classNames={cx}>
        <CaseStudiesSection company={company} />
    </Card>
);

const WrappedCaseStudies: FC = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <CaseStudies company={company} />;
};

export { WrappedCaseStudies as CaseStudies };
