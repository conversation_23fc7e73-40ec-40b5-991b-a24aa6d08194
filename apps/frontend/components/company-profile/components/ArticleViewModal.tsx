import React, { FC } from 'react';

import { Article } from '@repo/dcide-component-models';

import { Anchor, Group, Modal, Skeleton, Stack } from '@mantine/core';

import { useFile } from 'hooks/use-file';
import { useArticle } from 'hooks/use-article';

import { IKImage } from 'components/ik-image/IKImage';
import { RTEContent } from 'components/rte-content/RTEContent';

const ArticleViewModalSkeleton: FC<{
    onClose: () => void;
}> = ({ onClose }) => {
    return (
        <Modal opened onClose={onClose} title={<Skeleton h={18} w={200} />} size="xl">
            <Stack>
                <Skeleton w="100%" style={{ aspectRatio: '16 / 9' }} />

                <Skeleton h={8} />
                <Skeleton h={8} />
                <Skeleton h={8} />
                <Skeleton h={8} width="70%" />

                <Skeleton h={36} width={94} />
            </Stack>
        </Modal>
    );
};

const ArticleViewModal = ({ article, onClose }: { article: Article; onClose: () => void }) => {
    const { file } = useFile(article.file);
    const url = file?.url || article.teaser.button?.url;
    const label = file?.url ? 'Download the PDF' : 'More Info';

    const {
        teaser: { image, description },
        name,
    } = article;

    return (
        <Modal opened onClose={onClose} title={name} size="xl">
            <Stack>
                {image && <IKImage fileOrId={image as any} width={600} height={340} alt={name || ''} />}

                {description && <RTEContent content={description} />}

                {url && (
                    <Group>
                        <Anchor href={url} target="_blank" fw={600}>
                            {label}
                        </Anchor>
                    </Group>
                )}
            </Stack>
        </Modal>
    );
};

ArticleViewModal.Skeleton = ArticleViewModalSkeleton;

const WrappedArticleViewModal = ({ articleId, onClose }: { articleId: string; onClose: () => void }) => {
    const { article, isLoading } = useArticle(articleId);

    if (isLoading) {
        return <ArticleViewModal.Skeleton onClose={onClose} />;
    }

    if (!article) {
        return null;
    }

    return <ArticleViewModal article={article} onClose={onClose} />;
};

export { WrappedArticleViewModal as ArticleViewModal };
