import React from 'react';

import { But<PERSON>, Modal } from '@mantine/core';

import { CompanyProfile } from '@repo/dcide-component-models';

import { InAppSupport } from 'components/company-profile/components/InAppSupport';

type Props = { company: CompanyProfile; onClose: () => void };

const InAppSupportModal = ({ company, onClose }: Props) => {
    return (
        <Modal opened onClose={onClose} size="lg" withCloseButton={false}>
            <InAppSupport company={company} afterSubmit={onClose} />

            <Button fullWidth variant="transparent" onClick={onClose} mt={8}>
                Cancel
            </Button>
        </Modal>
    );
};

export { InAppSupportModal };
