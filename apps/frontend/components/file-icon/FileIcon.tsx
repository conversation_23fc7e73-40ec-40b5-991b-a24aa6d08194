import { MIME_TYPES } from '@mantine/dropzone';

import {
    TbFile,
    TbFileTypeCsv,
    TbFileTypeJpg,
    TbFileTypePdf,
    TbFileTypePng,
    TbFileTypeSvg,
    TbFileTypeXls,
    TbVideo,
} from 'react-icons/tb';

const icons: {
    [key: string]: any;
} = {
    [MIME_TYPES.csv]: TbFileTypeCsv,
    [MIME_TYPES.jpeg]: TbFileTypeJpg,
    [MIME_TYPES.mp4]: TbVideo,
    [MIME_TYPES.pdf]: TbFileTypePdf,
    [MIME_TYPES.png]: TbFileTypePng,
    [MIME_TYPES.svg]: TbFileTypeSvg,
    [MIME_TYPES.xls]: TbFileTypeXls,
    [MIME_TYPES.xlsx]: TbFileTypeXls,
};

const getFileIcon = (mimeType: string) => {
    return icons[mimeType] || TbFile;
};

const FileIcon = ({ mimeType, ...props }: { mimeType: string } & any) => {
    const Icon = getFileIcon(mimeType);

    return <Icon strokeWidth={1.5} {...props} />;
};

export { FileIcon, getFileIcon };
