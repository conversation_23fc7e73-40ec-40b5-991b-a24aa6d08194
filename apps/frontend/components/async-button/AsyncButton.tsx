import { FC, MouseEvent, useCallback, useState } from 'react';

import { But<PERSON> } from '@mantine/core';

type ButtonProps = Parameters<typeof Button<'button'>>[0] & { onClick: (event: MouseEvent) => Promise<void> };

const AsyncButton: FC<ButtonProps> = ({ onClick: passedOnClick, ...props }) => {
    const [isLoading, setIsLoading] = useState(false);

    const onClick = useCallback(
        async (event: MouseEvent) => {
            if (passedOnClick) {
                setIsLoading(true);
                await passedOnClick(event);
                setIsLoading(false);
            }
        },
        [passedOnClick],
    );

    return <Button onClick={onClick} loading={isLoading} {...props} />;
};

export { AsyncButton };
