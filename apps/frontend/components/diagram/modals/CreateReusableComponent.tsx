import React, { FC } from 'react';
import { DiagramComponentInstance } from '@repo/dcide-component-models';

import { SimpleGrid, Stack } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';
import { TextField } from 'components/forms/fields/TextField';

import { LocalNotificationService } from 'services/LocalNotificationService';
import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { LabelService } from 'components/diagram/services/LabelService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { ModalTitle } from 'components/modals/ModalTitle';

type FormValues = {
    name: string;
    manufacturer: string;
    partNumber: string;
};

const CreateReusableComponent: FC<
    ContextModalProps<{
        componentInstance: DiagramComponentInstance;
    }>
> = ({ id, innerProps, context }) => {
    const { componentInstance } = innerProps;

    const close = () => {
        context.closeModal(id);
    };

    const submit = async (values: FormValues) => {
        try {
            await ComponentInstanceService.createGenericReusableComponent(componentInstance!, values.name);
            DiagramSyncService.save();

            LocalNotificationService.showSuccess({
                message: 'Your component has been created!',
            });

            close();
        } catch (error: any) {
            LocalNotificationService.showError({
                message: error?.message ?? 'Error creating your component.',
            });
        }
    };

    return (
        <Form<FormValues>
            defaultValues={{
                name: LabelService.getComponentLabel(componentInstance!),
                manufacturer: componentInstance.manufacturer ?? '',
                partNumber: componentInstance.partNumber ?? '',
            }}
            onSubmit={submit}
        >
            <Stack gap="xs">
                <ModalTitle>Save component for re-use</ModalTitle>
                <TextField name="name" label="Name" required />
                <SimpleGrid cols={2} spacing={8}>
                    <TextField name="manufacturer" label="Manufacturer" required />
                    <TextField name="partNumber" label="Part number" required />
                </SimpleGrid>
                <FormSubmit>Save</FormSubmit>
            </Stack>
        </Form>
    );
};

export { CreateReusableComponent };
