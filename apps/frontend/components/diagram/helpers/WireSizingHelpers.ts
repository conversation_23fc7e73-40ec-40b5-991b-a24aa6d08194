import { DiagramConnection, WireSize } from '@repo/dcide-component-models';

const WireSizingHelpers = {
    swr: {
        wireSizings: (diagramId: string) => `/diagram/${diagramId}/wire-sizings`,
    },

    toAWG: (value: WireSize) => {
        return WireSize.toAWG(value);
    },

    hash: (connection: DiagramConnection) => {
        return JSON.stringify(connection);

        // TODO
        const parts = [
            connection.installation?.method,
            connection.conductorMaterial,
            connection.insulationMaterial,
            connection.installation.temperature.value,
            connection.powerFactor,
            connection.requirements.voltage.value,
            connection.requirements.current['L+'].value,
            connection.requirements.current['L-'].value,
            connection.requirements.current['PE']?.value,
            connection.length?.value,
        ]
            .filter(Boolean)
            .join(';');

        return parts;
    },
};

export { WireSizingHelpers };
