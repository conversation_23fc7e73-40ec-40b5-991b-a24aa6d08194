import { isNumber, uid } from 'radash';

import {
    Diagram,
    DiagramConnection,
    Zone,
    ZoneValidationError,
    ZoneValidationErrorType,
    ZoneValidator,
} from '@repo/dcide-component-models';
import { ConnectionService } from 'components/diagram/services';

const checkVoltageLevels: ZoneValidator['validator'] = ({ diagram, zones }) => {
    const errors: ZoneValidationError[] = [];

    zones.forEach((zone) => checkVoltageLevelsInZone(errors, diagram, zone));

    return errors;
};

const checkVoltageLevelsInZone = (errors: ZoneValidationError[], diagram: Diagram, zone: Zone) => {
    const { min, max } = getMinAndMaxVoltageForZone(diagram, zone);

    if (min > max) {
        errors.push({
            id: uid(6),
            type: ZoneValidationErrorType.VOLTAGE_LEVELS_MISMATCH,
            level: 'warning',
            payload: {
                zone,
                componentInstances: zone.componentInstances,
                voltage: { min, max },
            },
        });
    }
};

const getMinAndMaxVoltageForZone = (diagram: Diagram, zone: Zone) => {
    const result = {
        min: -Infinity,
        max: Infinity,
    };

    zone.connections.forEach((connection) => {
        const voltageType = ConnectionService.getVoltageType(connection);

        const isSinglePhase =
            voltageType === 'AC' && connection.lines.AC.L1 && !connection.lines.AC.L2 && !connection.lines.AC.L3;

        const isPoleToMidpoint =
            voltageType === 'DC' &&
            ((connection.lines.DC['L+'] && connection.lines.DC['M'] && !connection.lines.DC['L-']) ||
                (connection.lines.DC['L-'] && connection.lines.DC['M'] && !connection.lines.DC['L+']));
        // Note: introduce multiplier to always compare line-to-line voltages
        let multiplier = 1;

        if (voltageType === 'AC') {
            multiplier = isSinglePhase ? Math.sqrt(3) : 1;
        } else if (voltageType === 'DC') {
            multiplier = isPoleToMidpoint ? 2 : 1;
        }

        [connection.from, connection.to].forEach((endpoint) => {
            // @ts-ignore
            const endpointMinAndMax = getEndpointMinAndMaxVoltage(diagram, endpoint);

            if (endpointMinAndMax) {
                if (isNumber(endpointMinAndMax.min)) {
                    result.min = Math.max(result.min, multiplier * endpointMinAndMax.min);
                }

                if (isNumber(endpointMinAndMax.max)) {
                    result.max = Math.min(result.max, multiplier * endpointMinAndMax.max);
                }
            }
        });
    });

    return result;
};

const getEndpointMinAndMaxVoltage = (
    diagram: Diagram,
    endpoint: DiagramConnection['from' | 'to'],
): { min: number; max: number } | undefined => {
    const { componentInstanceId: id, port: portIndex } = endpoint;

    if (portIndex === null) return;

    const componentInstance = diagram.componentInstances[id];

    const { voltageType } = componentInstance.configuration.ports[portIndex];

    if (voltageType === null) return;

    const { ports } = componentInstance.specifications.electrical;

    if (portIndex >= ports.length) return;

    const port = ports[portIndex];

    // @ts-ignore
    const { min, max } = port?.[voltageType]?.voltage ?? {};

    return { min, max };
};

export { checkVoltageLevels, getMinAndMaxVoltageForZone };
