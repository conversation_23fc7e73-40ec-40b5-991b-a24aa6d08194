import { Diagram } from '@repo/dcide-component-models';

import { mutate } from 'swr';
import { ApiService } from 'services/ApiService';

import { SimulationProfileHelpers } from 'components/diagram/helpers/SimulationProfileHelpers';

import { simulationProfilesState } from 'components/diagram/state/simulation-profiles';

import { config } from 'config';

export type SimulationProfile = {
    id: string;
    name: string;
    type: 'load' | 'generation' | 'combined';
    data: string;
    project: string;
    diagram: string;
    createdAt: string;
    updatedAt: string;
};

export type SimulationProfileData = Record<string, string>[];

const SimulationProfileService = {
    create: async (diagram: Diagram, name: SimulationProfile['name'], type: SimulationProfile['type']) => {
        const response = await ApiService.post<{
            doc: SimulationProfile;
        }>(`${config.api.backend}/diagramSimulationProfiles`, {
            name,
            type,
            diagram: diagram.id,
            project: diagram.project,
        });

        await mutate(SimulationProfileHelpers.swr.list(diagram.id));

        return {
            ...response,
            simulationProfile: response.doc,
        };
    },

    list: async (diagram: Diagram) => {
        return ApiService.get<{
            docs: SimulationProfile[];
        }>(`${config.api.backend}/diagramSimulationProfiles?where[diagram][equals]=${diagram.id}`);
    },

    get: async (id: string) => {
        return ApiService.get<SimulationProfile>(`${config.api.backend}/diagramSimulationProfiles/${id}`);
    },

    update: async (id: string, data: Partial<SimulationProfile>) => {
        await ApiService.patch(`${config.api.backend}/diagramSimulationProfiles/${id}`, data);
        await mutate(SimulationProfileHelpers.swr.get(id));
    },

    duplicate: async (simulationProfile: SimulationProfile, name: string) => {
        const response = await ApiService.post<{
            doc: SimulationProfile;
        }>(`${config.api.backend}/diagramSimulationProfiles`, {
            name,
            type: simulationProfile.type,
            data: simulationProfile.data,
            diagram: simulationProfile.diagram,
            project: simulationProfile.project,
        });

        await mutate(SimulationProfileHelpers.swr.list(simulationProfile.diagram));

        return {
            ...response,
            simulationProfile: response.doc,
        };
    },

    delete: async (id: string) => {
        return ApiService.delete(`${config.api.backend}/diagramSimulationProfiles/${id}`);
    },

    activate: (id: string) => {
        simulationProfilesState.active = id;
    },

    deactivate: () => {
        simulationProfilesState.active = null;
    },
};

export { SimulationProfileService };
