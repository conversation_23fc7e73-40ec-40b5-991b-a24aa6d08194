import { useMemo } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { state as selectionState } from 'components/diagram/state/selection';
import { SelectionService } from 'components/diagram/services/SelectionService';

const useSelectedConnections = () => {
    const { selection } = useSnapshot(selectionState);

    return useMemo(() => {
        const slice = selection
            .filter((item) => {
                return item.type === 'connection';
            })
            .map((item) => item.id);

        return {
            selection: slice,
            selectionOnlyHasConnections: slice.length === selection.length,
            isSelectedConnection: SelectionService.isSelectedConnection,
            isOnlySelectedConnection: SelectionService.isOnlySelectedConnection,
        };
    }, [selection]);
};

export { useSelectedConnections };
