import { DiagramConnection } from '@repo/dcide-component-models';
import useSWR from 'swr/immutable';

import { WireSizingService } from 'components/diagram/services/WireSizingService';

import { useCurrentProject } from 'hooks/use-current-project';

const useConnectionWireSizing = (connection: DiagramConnection, voltageType: 'AC' | 'DC' | null) => {
    const project = useCurrentProject()!;
    const { standard, voltageDrop } = project?.wireSizing || {
        standard: 'IEC',
        voltageDrop: 0.05,
    };

    const key = [
        'calculate',
        standard,
        voltageDrop,
        voltageType,
        connection.installation?.method,
        connection.conductorMaterial,
        connection.insulationMaterial,
        connection.installation.temperature.value,
        connection.powerFactor,
        connection.requirements.voltage.value,
        connection.requirements.current['L+'].value,
        connection.requirements.current['L-'].value,
        connection.requirements.current['PE']?.value,
        connection.length?.value,
    ]
        .filter(Boolean)
        .join(';');

    const swr = useSWR(key, async () => {
        return key ? WireSizingService.getConnectionWireSize(connection, standard, voltageDrop, voltageType) : null;
    });

    let error = undefined;
    let wireSizing = undefined;

    if (swr.data && 'error' in swr.data) {
        error = swr.data.error;
    }

    if (swr.data && 'wireSizing' in swr.data) {
        wireSizing = swr.data.wireSizing;
    }

    return {
        ...swr,
        error,
        wireSizing,
    };
};

export { useConnectionWireSizing };
