import useSWRImmutable from 'swr/immutable';

import { ChatService } from 'components/diagram/services/ChatService';
import { ChatHelpers } from 'components/diagram/helpers/ChatHelpers';

import { useChat } from 'components/diagram/hooks/use-chat';
import { useCurrentProject } from 'hooks/use-current-project';
import { useCurrentUser } from 'hooks/use-current-user';

const useChatChannels = () => {
    const project = useCurrentProject()!;
    const user = useCurrentUser();

    const key = user ? ChatHelpers.swr.channels(project.id) : null;
    const fetcher = async () => ChatService.getChannels(project.id);

    const swr = useSWRImmutable(key, fetcher);
    const channels = swr?.data?.docs || [];

    const { channel: activeChannelId } = useChat();
    const activeChannel = channels.find((channel) => channel.id === activeChannelId);

    return {
        ...swr,
        channels,
        activeChannel,
    };
};

export { useChatChannels };
