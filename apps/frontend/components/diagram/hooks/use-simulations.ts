import useSWR from 'swr';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { useCurrentUser } from 'hooks/use-current-user';
import { useDiagram } from 'components/diagram/hooks/use-diagram';

import { SimulationService } from 'components/diagram/services/SimulationService';
import { SimulationHelpers } from 'components/diagram/helpers/SimulationHelpers';

import { simulationState } from 'components/diagram/state/simulation';

const useSimulations = () => {
    const snapshot = useSnapshot(simulationState);

    return snapshot;
};

const useSimulationsData = () => {
    const currentUser = useCurrentUser();
    const { id } = useDiagram();

    const key = currentUser && id ? SimulationHelpers.swr.simulations(id) : null;

    const swr = useSWR(key, async () => {
        return SimulationService.get(id);
    });

    return {
        ...swr,
        all: swr.data?.docs || [],
    };
};

export { useSimulations, useSimulationsData };
