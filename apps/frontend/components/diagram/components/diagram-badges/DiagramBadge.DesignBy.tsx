import React from 'react';
import Link from 'next/link';

import { Box, Group, Tooltip } from '@mantine/core';

import { CompanyProfile, PermissionDiagramElements, PublishedStatus } from '@repo/dcide-component-models';

import { useUser } from 'hooks/use-user';
import { usePermission } from 'hooks/use-permission';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useCurrentProject } from 'hooks/use-current-project';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { Avatar } from 'components/avatar/Avatar';
import { CompanyLogo } from 'components/company-logo';

import cx from './DiagramBadges.module.scss';

const DiagramBadgesDesignBy = () => {
    const project = useCurrentProject();
    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const showCreatedBy = !canEdit && project?.visibility === 'marketplace';
    const showTemplateOwner = project?.visibility === 'marketplace' && project?.template?.profile;
    const showReferenceOwner = project?.reference?.profile;

    if (showTemplateOwner) {
        return <DesignByTemplateOwner />;
    }

    if (showCreatedBy) {
        return <DesignCreatedBy />;
    }

    if (showReferenceOwner) {
        return <DesignByReferenceOwner />;
    }

    return null;
};

const DesignByTemplateOwner = () => {
    const project = useCurrentProject();

    const { company } = useCompanyProfile(project?.template?.profile ?? null);

    if (!company) {
        return null;
    }

    return <DesignByCompanyLogo company={company} />;
};

const DesignByReferenceOwner = () => {
    const project = useCurrentProject();

    const { company } = useCompanyProfile(project?.reference?.profile ?? null);

    const { companies } = useCurrentTeamCompanies();

    if (!company) {
        return null;
    }

    const userIsOwner = companies.find(({ id }) => id === company?.id);

    if (userIsOwner) {
        return null;
    }

    return (
        <DesignByCompanyLogo company={company} tooltip={`Design copied from a reference design by ${company.name}`} />
    );
};

const DesignByCompanyLogo = ({ company, tooltip }: { company: CompanyProfile; tooltip?: string }) => {
    const isProfilePublished = company?.status === PublishedStatus.PUBLISHED;

    const profileUrl = CompanyProfileHelpers.urls.view(company.slug);

    const content = (
        <Group gap={4}>
            Design by <CompanyLogo logos={company.logos} height={15} fallback={company.name} />
        </Group>
    );

    return (
        <>
            <Tooltip label={tooltip} position="bottom" withArrow disabled={!tooltip}>
                {isProfilePublished ? (
                    <Box className={cx.badge} component={Link} href={profileUrl} target="_blank">
                        {content}
                    </Box>
                ) : (
                    <Box className={cx.badge}>{content}</Box>
                )}
            </Tooltip>
        </>
    );
};

const DesignCreatedBy = () => {
    const project = useCurrentProject();
    const currentUser = useCurrentUser();

    // anonymous users cannot see other user's info, so don't make the user call
    const { user } = useUser(currentUser && project ? project.createdBy : undefined);

    if (user) {
        return (
            <Box className={cx.badge}>
                <Group gap={4}>
                    Design by
                    <Avatar user={user} size={20} />
                    {user.name}
                </Group>
            </Box>
        );
    }

    return <Box className={cx.badge}>Design by a DCIDE user</Box>;
};

export { DiagramBadgesDesignBy };
