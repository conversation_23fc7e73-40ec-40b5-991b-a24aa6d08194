.indicators {
    position: absolute;
    left: 50%;
    bottom: var(--mantine-spacing-xs);

    z-index: 99;

    display: flex;
    height: 32px;

    background-color: #ffffff;
    border: 1px solid var(--mantine-color-gray-3);
    border-radius: var(--mantine-radius-sm);

    transform: translateX(-50%);
}

.indicator {
    display: flex;
    align-items: center;
    justify-content: center;

    gap: calc(var(--mantine-spacing-xs) / 2);

    padding: 0 var(--mantine-spacing-xs);

    font-size: var(--mantine-font-size-xs);
    font-weight: 400;

    line-height: 1;
    white-space: nowrap;

    svg {
        width: 14px;
        height: 14px;

        stroke-width: 1.5px;
    }

    strong {
        font-weight: 500;
    }

    &[data-clickable='true'] {
        cursor: pointer;
    }
}

.indicator + .indicator {
    border-left: 1px solid var(--mantine-color-gray-3);
}
