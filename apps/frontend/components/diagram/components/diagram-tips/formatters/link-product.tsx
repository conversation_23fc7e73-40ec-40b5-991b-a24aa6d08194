import React from 'react';

import { Stack, Text } from '@mantine/core';

import { UserTip, UserTipFormatter } from '@repo/dcide-component-models';

import { DiagramTipAction } from 'components/diagram/components/diagram-tips/components/DiagramTipAction';
import { DiagramTipImage } from 'components/diagram/components/diagram-tips/components/DiagramTipImage';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { SidebarTab } from 'components/diagram/state/sidebar';

const linkProduct: UserTipFormatter = {
    name: UserTip.LINK_PRODUCT,
    title: 'Link existing products to components',
    description: (
        <Stack gap="xs">
            <DiagramTipImage tip={UserTip.LINK_PRODUCT} />
            <Text inherit>
                Or grab a product from the{' '}
                <DiagramTipAction onClick={() => SidebarService.openAddSidebar(SidebarTab.ADD_CATALOG)}>
                    Product catalog
                </DiagramTipAction>
            </Text>
        </Stack>
    ),
};

export { linkProduct };
