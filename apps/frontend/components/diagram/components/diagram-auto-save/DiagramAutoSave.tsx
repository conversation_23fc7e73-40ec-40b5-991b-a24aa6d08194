import { useEffect } from 'react';
import { useRouter } from 'next/router';

import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { useWindowEvent } from '@mantine/hooks';

import { syncingState } from 'components/diagram/state/syncing';

const DiagramAutoSave = () => {
    const router = useRouter();

    useEffect(() => {
        const handleUnload = (event: BeforeUnloadEvent) => {
            event.preventDefault();
        };

        if (syncingState.status === 'syncing') {
            addEventListener('beforeunload', handleUnload);

            return () => {
                removeEventListener('beforeunload', handleUnload);
            };
        }
    }, [syncingState]);

    useWindowEvent('unload', () => {
        DiagramSyncService.sync().then();
    });

    useEffect(() => {
        const block = async () => {
            await DiagramSyncService.sync();
        };

        router.events.on('routeChangeStart', block);

        return () => {
            router.events.off('routeChangeStart', block);
        };
    }, [router]);

    return null;
};

export { DiagramAutoSave };
