import type { FC } from 'react';
import { Box } from '@mantine/core';

import { useConnection } from 'components/diagram/hooks/use-connection';
import { useSelectedConnections } from 'components/diagram/hooks/selection/use-selected-connections';

import { ConnectionToolbar } from 'components/diagram/components/diagram-connections/components/ConnectionToolbar';

import { ConnectionService } from 'components/diagram/services/ConnectionService';

export const DiagramConnectionToolbarContainer: FC = () => {
    const { selection, isOnlySelectedConnection } = useSelectedConnections();
    const connection = useConnection(selection[0]);

    if (!isOnlySelectedConnection(connection?.id || '') || !connection) {
        return null;
    }

    const { getPointAtPosition, length } = ConnectionService.getPath(connection.from, connection.to)!;

    const centerOfConnection = getPointAtPosition(length / 2);
    const before = getPointAtPosition(length / 2 - 1);
    const after = getPointAtPosition(length / 2 + 1);

    const centerOfConnectionIsVertical = before.x === after.x;

    return (
        <Box
            style={{
                position: 'absolute',
                zIndex: 9999,

                left: centerOfConnection.x,
                top: centerOfConnection.y,

                width: 1,
                height: 1,
            }}
        >
            <ConnectionToolbar connection={connection} position={centerOfConnectionIsVertical ? 'right' : 'top'} />
        </Box>
    );
};
