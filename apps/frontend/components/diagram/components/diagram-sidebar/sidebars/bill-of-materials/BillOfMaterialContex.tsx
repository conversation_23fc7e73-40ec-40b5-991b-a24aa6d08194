import { createContext, useContext } from 'react';

import { OrderComponent } from 'services/BillOfMaterialsService';

export const BillOfMaterialsContext = createContext<{
    orderPrices: {
        totalPrice: {
            min: number | null;
            max: number | null;
        };
        orderComponents: {
            component: string;
            data: OrderComponent[];
        }[];
    } | null;
    installedByData: string[];
    furnishedByData: string[];
} | null>(null);

export const useBillOfMaterialsContext = () => {
    return useContext(BillOfMaterialsContext)!;
};
