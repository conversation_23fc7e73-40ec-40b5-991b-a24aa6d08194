import React, { FC, useEffect } from 'react';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { ZoneDetail } from './ZoneDetail';
import { ValidationOverview } from './ValidationOverview';

import { validationState } from 'components/diagram/state/validation';

import { ComponentInstanceDetail } from './ComponentInstanceDetail';

const Validations: FC = () => {
    const { activeZone, activeComponentInstance } = useSnapshot(validationState);

    useEffect(() => {
        return () => {
            validationState.activeZone = null;
            validationState.activeComponentInstance = null;
        };
    }, []);

    if (activeZone) {
        return <ZoneDetail />;
    }

    if (activeComponentInstance) {
        return <ComponentInstanceDetail id={activeComponentInstance} />;
    }

    return <ValidationOverview />;
};

export { Validations };
