import React from 'react';

import { Group, Stack, Title } from '@mantine/core';

import { ComponentSection } from '@repo/dcide-component-models';

import { ScrollNavTarget } from 'components/scroll-nav';

const DatasheetSection = ({
    id,
    title,
    afterTitle,
    children,
}: {
    id?: ComponentSection;
    title: string;
    afterTitle?: React.ReactNode;
    children: React.ReactNode;
}) => {
    const renderTitle = (
        <Group justify="flex-start" align="center" gap={8}>
            <Title order={2}>{title}</Title>
            {afterTitle ?? null}
        </Group>
    );

    return (
        <Stack gap="xs">
            {id ? (
                <ScrollNavTarget id={id} topSpacing={40}>
                    {renderTitle}
                </ScrollNavTarget>
            ) : (
                renderTitle
            )}
            {children}
        </Stack>
    );
};

export { DatasheetSection };
