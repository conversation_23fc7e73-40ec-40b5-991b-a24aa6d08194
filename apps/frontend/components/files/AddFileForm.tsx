import React, { FC } from 'react';
import { useController, useFormContext, useWatch } from 'react-hook-form';

import { Box, Button, FileButton, Flex, SegmentedControl, Space, Text, Title } from '@mantine/core';

import { CollectionWithFiles, ComponentFileType, ComponentFileTypes } from '@repo/dcide-component-models';
import { FileService, FileSizeLimitExceededError } from 'services/FileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { AddFileFormOnAdd } from './types';
import { Form } from 'components/forms/Form';
import { FormStatus } from 'components/forms/FormStatus';
import { FormSubmit } from 'components/forms/FormSubmit';
import { SelectField } from 'components/forms/fields/SelectField';
import { TextField } from 'components/forms/fields/TextField';

type AddFileFieldValues = {
    file?: File;
    url?: string;
    type: ComponentFileType;
    fileType: 'upload' | 'link';
};

export type AddFileFormProps = {
    collection: CollectionWithFiles;
    id: string;
    onAdd: AddFileFormOnAdd;
    defaultValues?: Partial<AddFileFieldValues>;
    diagramId?: string;
};

const AddFileForm: FC<AddFileFormProps> = ({ collection, id, onAdd, defaultValues = {}, diagramId }) => {
    return (
        <Box>
            <Title order={2}>Add file</Title>
            <Space h="md" />

            <Form<AddFileFieldValues>
                defaultValues={{ type: ComponentFileType.OTHER, fileType: 'upload', ...defaultValues }}
                onSubmit={async (values, { setSubmitError, reset }) => {
                    try {
                        if (values.fileType === 'upload' && values.file) {
                            const { newFile, updatedCollection } = await FileService.addFile({
                                group: `${collection}:${id}:files`,
                                associateWith: collection,
                                associatedId: id,
                                file: values.file,
                                type: values.type,
                                diagramId,
                            });

                            onAdd(updatedCollection, { file: newFile.id });

                            LocalNotificationService.showSuccess({ message: 'File added' });
                        } else if (values.fileType === 'link' && values.url) {
                            onAdd(
                                await FileService.addUrl({
                                    associateWith: collection,
                                    associatedId: id,
                                    url: values.url,
                                    type: values.type,
                                    diagramId,
                                }),
                                { url: values.url },
                            );

                            LocalNotificationService.showSuccess({ message: 'Link added' });
                        } else {
                            throw new Error('Either file or a URL must be provided');
                        }

                        reset({ fileType: values.fileType, url: '', file: undefined });
                    } catch (error: any) {
                        console.error('Error while adding file', error);

                        if (error instanceof FileSizeLimitExceededError) {
                            setSubmitError(`Error while adding file: ${error.message}`);
                        } else {
                            setSubmitError('Error while adding file');
                        }
                    }
                }}
            >
                <Flex direction="column" align="flex-start" gap="sm">
                    <AddFileFileTypeInput />
                    <AddFileFormField />
                    <AddFileFormSubmit />
                    <FormStatus />
                </Flex>
            </Form>
        </Box>
    );
};

const AddFileFormField: FC = () => {
    const { setValue } = useFormContext();
    const [file, fileType] = useWatch({ name: ['file', 'fileType'] });

    return (
        <Flex direction="column" gap="sm" w="100%">
            <SegmentedControl
                value={fileType}
                onChange={(value) => setValue('fileType', value)}
                data={[
                    { label: 'Upload a file', value: 'upload' },
                    { label: 'Link an existing file', value: 'link' },
                ]}
                mr="auto"
            />
            <Flex direction="column" gap={4}>
                <Flex gap="xs">
                    {fileType === 'upload' && <ProjectFileField />}
                    {fileType === 'link' && <ProjectFileUrlField />}
                    <ProjectFileTypeField />
                </Flex>
                {fileType === 'upload' && file && (
                    <Text c="dimmed" mt={4} size="xs">
                        Selected file: {file.name}
                    </Text>
                )}
            </Flex>
        </Flex>
    );
};

const ProjectFileField: FC = () => {
    const {
        field: { onChange },
    } = useController({ name: 'file' });

    const onUpload = (file: File | null) => {
        if (file) {
            onChange(file);
        }
    };

    return (
        <FileButton onChange={onUpload}>
            {(props) => (
                <Box>
                    <Text>File</Text>
                    <Button variant="default" {...props}>
                        Select a file
                    </Button>
                </Box>
            )}
        </FileButton>
    );
};

const ProjectFileUrlField: FC = () => {
    return (
        <TextField
            name="url"
            label="Link eg. LucidChart, Google Drive"
            placeholder="https://"
            style={{ flexGrow: 1 }}
        />
    );
};

const ProjectFileTypeField: FC = () => {
    return <SelectField name="type" label="Type" defaultValue="other" data={ComponentFileTypes.options} />;
};

const AddFileFileTypeInput: FC = () => {
    const { register } = useFormContext();

    return <input type="hidden" {...register('fileType')} />;
};

const AddFileFormSubmit: FC = () => {
    const [file, url, fileType] = useWatch({ name: ['file', 'url', 'fileType'] });

    return (
        <FormSubmit disabled={(fileType === 'link' && !url) || (fileType === 'upload' && !file)}>
            Add new file
        </FormSubmit>
    );
};

export { AddFileForm };
