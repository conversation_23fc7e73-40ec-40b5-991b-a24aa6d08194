import React, { FC } from 'react';

import { User, WebSocketEvent } from '@repo/dcide-component-models';

import { NotificationService } from 'services/NotificationService';
import { IntercomService } from 'services/IntercomService';

import { useChannel, ChannelProvider } from 'ably/react';

const Listeners: FC<{ userId: string }> = ({ userId }) => {
    useChannel(userId, async (message) => {
        switch (message.name) {
            case WebSocketEvent.USER_REFRESH_NOTIFICATIONS: {
                await NotificationService.refreshInbox();
                break;
            }
            case WebSocketEvent.USER_RECEIVE_NOTIFICATION: {
                NotificationService.receiveNotification(message.data.notification);
                break;
            }
            case WebSocketEvent.INTERCOM_REFRESH_COMPONENT_CHANNELS: {
                await IntercomService.refreshComponentChannels(message.data.componentId);
                break;
            }
            case WebSocketEvent.INTERCOM_REFRESH_COMPANY_CHANNELS: {
                await IntercomService.refreshCompanyChannels(message.data.companyId);
                break;
            }
            case WebSocketEvent.INTERCOM_REFRESH_PROJECT_CHANNELS: {
                await IntercomService.refreshProjectChannels(message.data.projectId);
                break;
            }
            case WebSocketEvent.INTERCOM_REFRESH_CHANNEL_MESSAGES: {
                await IntercomService.refreshChannelMessages(message.data.channelId);
                break;
            }
        }
    });

    return null;
};

const RealtimeUserProvider: FC<{
    user: User | null;
    children: React.ReactNode;
}> = ({ user = null, children }) => {
    return user ? (
        <ChannelProvider channelName={user.id}>
            <Listeners userId={user.id} />
            {children}
        </ChannelProvider>
    ) : (
        children
    );
};

export { RealtimeUserProvider };
