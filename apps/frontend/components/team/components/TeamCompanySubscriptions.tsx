import React, { FC } from 'react';

import { Anchor, Stack, Title, Text } from '@mantine/core';
import Link from 'next/link';

import { Team } from '@repo/dcide-component-models';

import { useTeamCompanies } from 'hooks/use-current-team-companies';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileSubscription } from 'components/company-subscription/CompanyProfileSubscription';

const TeamCompanySubscriptions: FC<{ team: Team }> = ({ team }) => {
    const { companies } = useTeamCompanies(team.id);

    return team && companies.length > 0 ? (
        <Stack gap="sm">
            <Title order={2}>
                {companies.length === 1 ? 'Company Profile Subscription' : 'Company Profile Subscriptions'}
            </Title>
            {companies.length === 1 ? (
                <CompanyProfileSubscription team={team} profileId={companies[0].id} />
            ) : (
                <>
                    <Text>You can manage your profile subscription in your profile settings:</Text>
                    <ul style={{ marginBottom: 0 }}>
                        {companies.map((company) => (
                            <li key={company.id}>
                                <Anchor
                                    component={Link}
                                    href={CompanyProfileHelpers.urls.view(company.slug, 'subscription')}
                                >
                                    {company.name}
                                </Anchor>
                            </li>
                        ))}
                    </ul>
                </>
            )}
        </Stack>
    ) : null;
};

export { TeamCompanySubscriptions };
