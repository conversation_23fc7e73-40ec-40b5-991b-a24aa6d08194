import { FC, CSSProperties, ReactNode } from 'react';

import { File } from '@repo/dcide-component-models';

import { Flex, Loader } from '@mantine/core';
import { IKImage } from 'components/ik-image/IKImage';

const Thumbnail: FC<{
    image?: File;
    isLoading?: boolean;
    style?: CSSProperties;
    showEmpty?: boolean;
    fallback?: ReactNode;
}> = ({ image, isLoading, style, showEmpty, fallback = '' }) => {
    if (!image && showEmpty)
        return (
            <Flex
                align="center"
                justify="center"
                bg={isLoading ? 'white' : 'gray.0'}
                c="gray.5"
                fw={500}
                p={4}
                style={style}
                ta="center"
                fz="xs"
                lh="1"
            >
                {isLoading ? <Loader size="xs" color="gray.5" /> : fallback}
            </Flex>
        );

    return image ? (
        <IKImage
            fileOrId={image}
            width={350}
            alt=""
            style={{
                ...style,
                objectFit: 'contain',
            }}
        />
    ) : null;
};

export { Thumbnail };
