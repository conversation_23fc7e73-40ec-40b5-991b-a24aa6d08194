import React, { FC } from 'react';

import { Stack, Box } from '@mantine/core';

import { ComponentOverviewHit } from 'components/component-overview';
import { CarouselSection } from 'components/section/CarouselSection';
import { ComponentLanding } from 'components/component-landing/ComponentLanding';

import { useCurrentSavedItems } from 'hooks/use-current-saved-items';
import { useComponent } from 'hooks/use-component';

const ComponentLandingSaved: FC = () => {
    const { components: savedComponents } = useCurrentSavedItems();

    return savedComponents.length ? (
        <Stack gap="xs">
            <Box>
                <ComponentLanding.Title>Liked Products</ComponentLanding.Title>
            </Box>
            <CarouselSection nbCols={4}>
                {savedComponents.map((savedComponent) => (
                    <SavedComponent id={savedComponent.item.value} key={savedComponent.item.value} />
                ))}
            </CarouselSection>
        </Stack>
    ) : null;
};

const SavedComponent: FC<{ id: string }> = ({ id }) => {
    const { component } = useComponent(id);

    return component ? <ComponentOverviewHit component={component} /> : null;
};

export { ComponentLandingSaved };
