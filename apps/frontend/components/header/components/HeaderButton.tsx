import React from 'react';

import { <PERSON><PERSON>, ButtonProps, createPolymorphicComponent, Tooltip } from '@mantine/core';

type Props = { children: React.ReactNode } & ButtonProps;

const HeaderButtonComponent = (props: { children: React.ReactNode } & ButtonProps) => {
    return (
        <Tooltip label={props.children} offset={10} visibleFrom="md" hiddenFrom="xl">
            <Button size="xs" variant="transparent" color="white" {...props} />
        </Tooltip>
    );
};

const HeaderButton = createPolymorphicComponent<'button', Props>((props: Props) => {
    return <HeaderButtonComponent {...props} />;
});

const HeaderLink = createPolymorphicComponent<'a', Props>((props: Props) => {
    return <HeaderButtonComponent {...props} />;
});

export { HeaderButton, HeaderLink };
