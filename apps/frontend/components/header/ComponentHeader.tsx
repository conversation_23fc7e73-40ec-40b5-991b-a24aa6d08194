import React, { FC } from 'react';

import { IoAddSharp, IoDuplicateSharp } from 'react-icons/io5';

import { Component, PermissionComponent } from '@repo/dcide-component-models';

import { TextHelpers } from 'helpers/TextHelpers';

import { useCurrentUser } from 'hooks/use-current-user';
import { useComponentPermissions } from 'hooks/use-component-permissions';

import { DropDown } from 'components/dropdown/DropDown';

import { Header } from './Header';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

type Props = {
    component: Component;
};

export const ComponentHeader: FC<Props> = ({ component }) => {
    const breadcrumbs = useBreadcrumbs(component);

    return <Header breadcrumbs={breadcrumbs} rightSection={<RightSection />} />;
};

const RightSection: FC = () => {
    return <></>;
};

const useBreadcrumbs = (component: Component): React.ReactNode[] => {
    const user = useCurrentUser();

    if (!component) return [null];
    if (!user) return [null];

    const items = [
        <ProductCatalogBreadcrumb component={component} key="product-catalog" />,
        <ComponentBreadcrumb component={component} key="component" />,
    ];

    return items;
};

const ProductCatalogBreadcrumb: FC<Props> = () => {
    return <DropDown title="Products" href={RouterHelpers.urls.searchTab('products')} />;
};

const ComponentBreadcrumb: FC<Props> = ({ component }) => {
    const user = useCurrentUser();

    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    return (
        <DropDown title={TextHelpers.getTextWithEllipsis(component.name, 12)}>
            {user && (
                <>
                    <DropDown.Option leftSection={<IoAddSharp size={16} />} href={ComponentHelpers.urls.create()}>
                        Sign up your product
                    </DropDown.Option>
                    {canEdit && (
                        <DropDown.Option
                            leftSection={<IoDuplicateSharp size={16} />}
                            href={ComponentHelpers.urls.duplicate(component.id)}
                        >
                            Add variant
                        </DropDown.Option>
                    )}
                </>
            )}
        </DropDown>
    );
};
