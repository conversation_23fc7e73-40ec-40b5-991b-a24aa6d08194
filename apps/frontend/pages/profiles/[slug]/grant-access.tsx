import React from 'react';

import { GetServerSideProps } from 'next';

import { CompanyProfile } from '@repo/dcide-component-models';

import { getParam } from 'helpers/getParam';
import { getSession } from 'helpers/getSession';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { GrantCompanyAccess } from 'components/grant-company-access/GrantCompanyAccess';

const ManufacturerAccess = ({ company }: { company: CompanyProfile }) => {
    return <GrantCompanyAccess company={company} />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res, params }) => {
    const session = await getSession(req, res);

    if (!session?.token) {
        return { notFound: true };
    }

    const slug = getParam(params, 'slug');

    if (!slug) {
        return { notFound: true };
    }

    const companies = await CompanyProfileService.getBySlug(slug);
    const company = companies?.docs?.[0] ?? null;

    if (!company) {
        return { notFound: true };
    }

    return {
        props: {
            company,
        },
    };
};

export default ManufacturerAccess;
