.validator {
    display: grid;
    gap: var(--mantine-spacing-xl);
}

.intro {
    text-align: center;
}

.split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--mantine-spacing-xl);

    height: 60vh;
}

.input {
    position: relative;

    :global(.mantine-Textarea-root),
    :global(.mantine-Textarea-wrapper),
    :global(.mantine-Textarea-input) {
        position: absolute;
        inset: 0;
    }
}

.output {
    position: relative;

    padding: var(--mantine-spacing-xs);

    font-size: var(--input-font-size);
    font-weight: 500;

    background-color: #ffffff;

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-xs);
}

.success {
    position: absolute;
    inset: 0;

    display: flex;
    justify-content: center;
    align-items: center;

    font-size: 18px;
    font-weight: 600;
    text-align: center;

    background-color: var(--mantine-color-green-0);
    color: var(--mantine-color-green-6);
}

.issues {
    display: grid;
    gap: var(--mantine-spacing-md);
}

.issue {
}

.issueMessage {
    color: var(--mantine-color-red-6);
}

.issuePath {
    font-size: 11px;

    color: var(--mantine-color-gray-6);
}

.examples {
    text-align: center;
}

.example {
    &:hover {
        text-decoration: underline;
    }
}
