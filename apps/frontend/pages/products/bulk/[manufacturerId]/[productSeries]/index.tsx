import { getParam } from 'helpers';

import { GetServerSideProps } from 'next';
import { ProductSeriesEditor } from 'components/component-bulk-editor/ProductSeriesEditor';

const ProductSeriesEditPage = ({
    manufacturerId,
    productSeries,
}: {
    manufacturerId: string;
    productSeries: string;
}) => <ProductSeriesEditor manufacturerId={manufacturerId} productSeries={productSeries} />;

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const manufacturerId = getParam(params, 'manufacturerId');
    const productSeries = getParam(params, 'productSeries');

    if (!manufacturerId || !productSeries) {
        return { notFound: true };
    }

    return {
        props: {
            manufacturerId,
            productSeries,
        },
    };
};

export default ProductSeriesEditPage;
