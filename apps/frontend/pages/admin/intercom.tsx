import NotFound from 'pages/404';

import { useCurrentUser } from 'hooks/use-current-user';

import { Page } from 'components/page';
import { IntercomOverview } from 'components/intercom-overview/IntercomOverview';

const IntercomPage = () => {
    const user = useCurrentUser();

    if (!user?.developer) return <NotFound />;

    return (
        <Page
            showBackground
            hideFooter
            hideLicenseAgreement
            title="Intercom Overview"
            breadcrumbs={{
                isSticky: false,
                showToggle: true,
            }}
        >
            <Page.WideContent>
                <IntercomOverview />
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps = async () => {
    return {
        props: {},
    };
};

export default IntercomPage;
