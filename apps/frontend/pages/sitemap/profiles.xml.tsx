import { GetServerSideProps } from 'next';
import { ApiService } from 'services/ApiService';

const BASE_URL = 'https://dcide.app';

import { config } from 'config';

const SiteMap = () => {
    // getServerSideProps will do the heavy lifting
};

export const getServerSideProps: GetServerSideProps = async ({ res }) => {
    const { profiles } = await ApiService.get<{
        profiles: { slug: string }[];
    }>(`${config.api.backend}/globals/sitemap`);

    const urls: string[] = [];

    profiles.forEach((profile) => {
        urls.push(`<url><loc>${BASE_URL}/profiles/${profile.slug}</loc></url>`);
    });

    const sitemap = `
        <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">
            ${urls.join('\n')}
        </urlset>
    `;

    res.setHeader('Content-Type', 'text/xml');
    res.write(sitemap);
    res.end();

    return {
        props: {},
    };
};

export default SiteMap;
