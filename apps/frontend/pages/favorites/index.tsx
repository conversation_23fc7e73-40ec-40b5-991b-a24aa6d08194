import React, { <PERSON> } from 'react';

import { GetServerSideProps } from 'next';

import { getSession } from 'helpers/getSession';

import { SavedItems } from 'components/saved-items/SavedItems';

const Saved: FC = () => {
    return <SavedItems />;
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    const session = await getSession(req, res);

    if (!session.token) {
        return {
            notFound: true,
        };
    }

    return {
        props: {},
    };
};

export default Saved;
